'use client'

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { QrCode, Download, Loader2 } from 'lucide-react'
import type { QrDownloadState, QrDownloadActions, Class, User } from '../types'
import { useState } from 'react'

interface QrDownloadDialogProps {
  // State from hook
  showDialog: boolean
  downloadType: QrDownloadState['downloadType']
  classId: string
  isDownloading: boolean
  progress: QrDownloadState['progress']

  // Actions from hook
  closeDialog: QrDownloadActions['closeDialog']
  setDownloadType: QrDownloadActions['setDownloadType']
  setClassId: QrDownloadActions['setClassId']
  downloadQrCodes: QrDownloadActions['downloadQrCodes']

  // Data
  users: User[]
  classes: Class[]
  selectedUsers: Set<number>
  isLoadingClasses: boolean
}

export function QrDownloadDialog({
  showDialog,
  downloadType,
  classId,
  isDownloading,
  progress,
  closeDialog,
  setDownloadType,
  setClassId,
  downloadQrCodes,
  users,
  classes,
  selectedUsers,
  isLoadingClasses,
}: QrDownloadDialogProps) {
  // Add gender filter state
  const [genderFilter, setGenderFilter] = useState<'all' | 'male' | 'female'>('all')

  // Helper to filter users by gender
  const filterByGender = (user: User) => {
    if (genderFilter === 'all') return true
    return user.gender === genderFilter
  }

  const handleSubmit = () => {
    downloadQrCodes(selectedUsers, genderFilter)
  }

  const getSelectedStudentCount = () => {
    return Array.from(selectedUsers).filter(id => {
      const user = users.find(u => u.id === id && filterByGender(u))
      return user && user.role === 'student'
    }).length
  }

  const getClassStudentCount = (classId: string) => {
    return users.filter(
      u => u.classId === parseInt(classId) && u.role === 'student' && filterByGender(u)
    ).length
  }

  const getAllStudentCount = () => {
    return users.filter(u => u.role === 'student' && filterByGender(u)).length
  }

  const isDownloadDisabled = () => {
    if (isDownloading) return true
    switch (downloadType) {
      case 'selected':
        return getSelectedStudentCount() === 0
      case 'class':
        return !classId || getClassStudentCount(classId) === 0
      case 'all':
        return getAllStudentCount() === 0
      default:
        return true
    }
  }

  const genderLabel = (gender?: 'male' | 'female') => {
    if (gender === 'male') return 'Laki-laki'
    if (gender === 'female') return 'Perempuan'
    return '-'
  }

  return (
    <Dialog open={showDialog} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <QrCode className="mr-2 h-5 w-5 text-indigo-600" />
            Download QR Code Siswa
          </DialogTitle>
          <DialogDescription>Pilih opsi download QR code untuk siswa</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Gender Filter */}
          <div className="space-y-2">
            <Label htmlFor="qrDownloadGender">Filter Gender:</Label>
            <Select
              value={genderFilter}
              onValueChange={v => setGenderFilter(v as 'all' | 'male' | 'female')}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua Gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Gender</SelectItem>
                <SelectItem value="male">Laki-laki</SelectItem>
                <SelectItem value="female">Perempuan</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Download Type Selection */}
          <div className="space-y-3">
            <Label>Pilih Jenis Download:</Label>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="download-all"
                  name="downloadType"
                  value="all"
                  checked={downloadType === 'all'}
                  onChange={e => setDownloadType(e.target.value as 'all' | 'selected' | 'class')}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                />
                <label htmlFor="download-all" className="text-sm font-medium">
                  Semua Siswa ({getAllStudentCount()} siswa)
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="download-selected"
                  name="downloadType"
                  value="selected"
                  checked={downloadType === 'selected'}
                  onChange={e => setDownloadType(e.target.value as 'all' | 'selected' | 'class')}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                  disabled={selectedUsers.size === 0}
                />
                <label
                  htmlFor="download-selected"
                  className={`text-sm font-medium ${selectedUsers.size === 0 ? 'text-gray-400' : ''}`}
                >
                  Siswa Terpilih ({getSelectedStudentCount()} siswa)
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="download-class"
                  name="downloadType"
                  value="class"
                  checked={downloadType === 'class'}
                  onChange={e => setDownloadType(e.target.value as 'all' | 'selected' | 'class')}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                />
                <label htmlFor="download-class" className="text-sm font-medium">
                  Berdasarkan Kelas
                </label>
              </div>
            </div>
          </div>

          {/* Class Selection (only show when class option is selected) */}
          {downloadType === 'class' && (
            <div className="space-y-2">
              <Label htmlFor="qrDownloadClassId">Pilih Kelas:</Label>
              {isLoadingClasses ? (
                <div className="flex items-center space-x-2 py-2">
                  <Loader2 className="h-4 w-4 animate-spin text-indigo-600" />
                  <span className="text-sm text-slate-500">Memuat data kelas...</span>
                </div>
              ) : classes.length > 0 ? (
                <>
                  <Select value={classId} onValueChange={setClassId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih kelas" />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map(cls => {
                        const studentCount = users.filter(
                          u => u.classId === cls.id && u.role === 'student' && filterByGender(u)
                        ).length
                        return (
                          <SelectItem
                            key={cls.id}
                            value={String(cls.id)}
                            disabled={studentCount === 0}
                          >
                            {cls.name} ({studentCount} siswa)
                            {studentCount === 0 && (
                              <span className="ml-2 text-amber-600">- Tidak ada siswa</span>
                            )}
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>

                  {/* Warning if all classes have no students */}
                  {classes.every(
                    cls =>
                      users.filter(
                        u => u.classId === cls.id && u.role === 'student' && filterByGender(u)
                      ).length === 0
                  ) && (
                    <div className="rounded-md bg-amber-50 p-3 text-sm text-amber-800 dark:bg-amber-900/30 dark:text-amber-200">
                      <p className="font-medium">⚠️ Peringatan:</p>
                      <p>
                        Semua kelas tidak memiliki siswa. Silakan tambahkan siswa ke kelas terlebih
                        dahulu.
                      </p>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-sm text-amber-600 dark:text-amber-400">
                  <p>Tidak ada kelas tersedia.</p>
                </div>
              )}
            </div>
          )}

          {/* Progress Display */}
          {progress && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Progress:</span>
                <span className="text-sm text-slate-500">
                  {progress.current}/{progress.total}
                </span>
              </div>
              <div className="h-2 w-full rounded-full bg-gray-200">
                <div
                  className="h-2 rounded-full bg-indigo-600 transition-all duration-300"
                  style={{
                    width:
                      progress.total > 0 ? `${(progress.current / progress.total) * 100}%` : '0%',
                  }}
                ></div>
              </div>
              <p className="text-sm text-slate-600">{progress.status}</p>
            </div>
          )}

          {/* Info Box */}
          <div className="rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
            <p className="mb-1 font-medium">Informasi:</p>
            <ul className="space-y-1 text-xs">
              <li>• QR code akan diunduh dalam format ZIP</li>
              <li>• Setiap file QR code berformat PNG (300x300px)</li>
              <li>• Nama file berisi nama siswa dan kode unik</li>
              <li>• File README.txt berisi petunjuk penggunaan</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={closeDialog} disabled={isDownloading}>
            Batal
          </Button>
          <Button variant="default" onClick={handleSubmit} disabled={isDownloadDisabled()}>
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span>Mengunduh...</span>
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                <span>Download QR Codes</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
