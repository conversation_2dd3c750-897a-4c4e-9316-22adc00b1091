import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import type { QrDownloadState, QrDownloadActions, QrDownloadType, User } from '../types'

export function useQrDownload(users: User[]): QrDownloadState & QrDownloadActions {
  const { toast } = useToast()

  const [showDialog, setShowDialog] = useState(false)
  const [downloadType, setDownloadType] = useState<QrDownloadType>('all')
  const [classId, setClassId] = useState('')
  const [isDownloading, setIsDownloading] = useState(false)
  const [progress, setProgress] = useState<QrDownloadState['progress']>(null)

  const openDialog = () => {
    setShowDialog(true)
    setDownloadType('all')
    setClassId('')
    setProgress(null)
  }

  const closeDialog = () => {
    setShowDialog(false)
  }

  const downloadQrCodes = async (
    selectedUserIds: Set<number>,
    gender: 'male' | 'female' | 'all' = 'all'
  ) => {
    try {
      setIsDownloading(true)
      setProgress({ current: 0, total: 0, status: 'Mempersiapkan download...' })

      // Validate download type and check for students
      if (downloadType === 'selected') {
        const selectedStudentIds = Array.from(selectedUserIds).filter(id => {
          const user = users.find(u => u.id === id)
          return user && user.role === 'student'
        })

        if (selectedStudentIds.length === 0) {
          toast({
            title: 'Tidak Ada Siswa Terpilih',
            description: 'Harap pilih minimal satu siswa untuk download QR code',
            variant: 'destructive',
          })
          return
        }
      } else if (downloadType === 'class') {
        if (!classId) {
          toast({
            title: 'Kelas Belum Dipilih',
            description: 'Harap pilih kelas untuk download QR code',
            variant: 'destructive',
          })
          return
        }

        // Check if the selected class has any students
        const studentsInClass = users.filter(
          user => user.role === 'student' && user.classId === parseInt(classId)
        )

        if (studentsInClass.length === 0) {
          toast({
            title: 'Kelas Tidak Memiliki Siswa',
            description:
              'Kelas yang dipilih tidak memiliki siswa. Silakan pilih kelas lain atau tambahkan siswa ke kelas ini terlebih dahulu.',
            variant: 'destructive',
          })
          return
        }
      } else if (downloadType === 'all') {
        // Check if there are any students at all
        const allStudents = users.filter(user => user.role === 'student')

        if (allStudents.length === 0) {
          toast({
            title: 'Tidak Ada Siswa',
            description:
              'Tidak ada siswa yang terdaftar dalam sistem. Silakan tambahkan siswa terlebih dahulu.',
            variant: 'destructive',
          })
          return
        }
      }

      // Prepare request data
      const requestData: any = { downloadType }

      if (downloadType === 'selected') {
        const selectedStudentIds = Array.from(selectedUserIds).filter(id => {
          const user = users.find(u => u.id === id)
          return user && user.role === 'student'
        })
        requestData.studentIds = selectedStudentIds
      } else if (downloadType === 'class') {
        requestData.classId = classId
      }
      if (gender && gender !== 'all') {
        requestData.gender = gender
      }

      setProgress({ current: 1, total: 3, status: 'Mengirim permintaan ke server...' })

      // Make API request
      const response = await fetch('/api/students/bulk-qr-download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const errorData = await response.json()

        // Handle specific error cases
        if (response.status === 404 && errorData.error?.includes('No students found')) {
          toast({
            title: 'Tidak Ada Siswa Ditemukan',
            description:
              'Tidak ada siswa yang memenuhi kriteria yang dipilih. Silakan periksa kembali pilihan Anda.',
            variant: 'destructive',
          })
          return
        }

        // Handle other errors
        const errorMessage = errorData.error || 'Gagal mengunduh QR codes'
        throw new Error(errorMessage)
      }

      setProgress({ current: 2, total: 3, status: 'Mengunduh file ZIP...' })

      // Download the ZIP file
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Get filename from response headers or create default
      const contentDisposition = response.headers.get('content-disposition')
      let filename = 'qr-codes.zip'
      if (contentDisposition) {
        const match = contentDisposition.match(/filename="(.+)"/)
        if (match && match[1]) {
          filename = match[1]
        }
      }
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      setProgress({ current: 3, total: 3, status: 'Selesai!' })
      setTimeout(() => setShowDialog(false), 1000)
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal mengunduh QR codes',
        variant: 'destructive',
      })
    } finally {
      setIsDownloading(false)
    }
  }

  return {
    // State
    showDialog,
    downloadType,
    classId,
    isDownloading,
    progress,

    // Actions
    openDialog,
    closeDialog,
    setDownloadType,
    setClassId,
    downloadQrCodes,
  }
}
