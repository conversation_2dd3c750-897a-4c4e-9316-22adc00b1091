# TODO: Fix School Reports Daily Data Issue

## Status: 🔍 IN PROGRESS - DEBUGGING

### Problem:

School Reports page (<PERSON><PERSON><PERSON>) not showing data correctly when filtering by "Hari ini" (Today):

- "<PERSON><PERSON><PERSON>" (Entry) showing 0
- "Terlambat" (Late Entry) showing 0
- "<PERSON><PERSON> & Sakit" (Excused Absence & Sick) showing 0

### Debugging Steps Implemented:

1. ✅ Added console logging for raw API data
2. ✅ Added debugging for daily data calculations
3. ✅ Added validation for data structure
4. ✅ Added API URL logging
5. ✅ Added stats reset to prevent stale data
6. ✅ Improved boolean filtering with strict equality (=== true)

### Next Steps to Debug:

1. **Check API Response**: Verify `/api/absence/reports?date=today&reportType=school` returns correct data
2. **Data Structure Validation**: Ensure API returns school attendance data with correct boolean fields
3. **Field Mapping**: Verify that the API response has the expected fields:
   - `entry: boolean`
   - `lateEntry: boolean`
   - `excusedAbsence: boolean`
   - `sick: boolean`
4. **API Parameter Validation**: Ensure `reportType=school` is working correctly

### Expected API Response Structure:

```typescript
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean // Should be true/false
  entryTime: string | null
  lateEntry: boolean // Should be true/false
  lateEntryTime: string | null
  excusedAbsence: boolean // Should be true/false
  excusedAbsenceTime?: string | null
  temporaryLeave: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave: boolean
  returnFromLeaveTime?: string | null
  sick: boolean // Should be true/false
  sickTime?: string | null
}
```

### Debugging Commands to Run:

1. Open browser console on Laporan Sekolah page
2. Select "Hari ini" filter
3. Check console logs for:
   - API URL being called
   - Raw data received
   - Daily calculations results
   - Final stats

### Files Modified:

- `app/admin/school-reports/page.tsx` - Added extensive debugging
