# User Management Refactoring & Bulk QR Code Download - TODO

## Overview

Refactoring user management to separate student and admin management, plus implementing bulk QR code download feature.

## High Priority Tasks

### 1. Create Admin Management Page

- [] Create `/app/admin/admins/page.tsx` for admin and super admin users
- [] Implement admin-specific CRUD operations
- [] Add permission checks (super admin only)
- [] Create admin-specific interfaces and types

### 2. Update Navigation

- [] Update `AdminBottomNav` component to include "Admin" menu item
- [] Add appropriate icons and routing
- [] Implement permission-based visibility

### 3. Refactor Existing Users Page

- [] Rename current page to "Manajemen Siswa" (Student Management)
- [] Filter to show only student users
- [] Remove admin/super admin related functionality
- [] Keep bulk operations for students only

## Medium Priority Tasks

### 4. Implement Bulk QR Code Download

- [✅] Install required dependencies (`jszip`, `qrcode`, `file-saver`)
- [✅] Create API endpoint `/api/students/bulk-qr-download`
- [✅] Implement server-side QR generation and ZIP creation
- [✅] Add UI components for download options:
  - [✅] Download all students
  - [✅] Download selected students
  - [✅] Download by class
- [✅] Add progress dialog with real-time updates
- [✅] Implement chunked processing for performance

### 5. Create Shared Components (HOLD)

- [ ] Extract common user management logic into hooks
- [ ] Create reusable components for user forms
- [ ] Implement shared permission checking utilities

### 6. API Endpoints

- [] Create `/api/admins/` endpoints for admin management
- [] Update existing `/api/users/` endpoints to handle separation
- [ ] Implement bulk QR generation endpoint

## Low Priority Tasks

### 7. Performance Optimization (HOLD)

- [ ] Implement database indexing for user queries (HOLD)
- [ ] Add caching for frequently accessed data (HOLD)
- [ ] Optimize QR code generation for large batches (HOLD)

## Technical Implementation Details

### Directory Structure

```
app/admin/
├── users/page.tsx          # Student Management (refactored)
├── admins/page.tsx         # Admin Management (new)
└── ...

components/
├── admin-bottom-nav.tsx    # Updated navigation
├── shared/
│   ├── user-form.tsx      # Shared user form component
│   ├── bulk-operations.tsx # Shared bulk operations
│   └── qr-download.tsx    # QR download components
└── ...

api/
├── users/                  # Student-focused endpoints
├── admins/                 # Admin-focused endpoints
└── students/
    └── bulk-qr-download/   # QR generation endpoint
```

### Key Features by Page

#### Student Management (`/admin/users`)

- View, add, edit, delete students
- Bulk upload students via CSV
- Bulk update student classes
- Bulk delete students
- **Bulk QR code download** (new feature)
- Search and filter by class
- Accessible by super admins

#### Admin Management (`/admin/admins`)

- View, add, edit, delete admin users
- Role management (admin/super admin)
- Permission management
- Search and filter capabilities
- Accessible by super admins only

## Dependencies to Install

```bash
npm install jszip qrcode file-saver
npm install --save-dev @types/qrcode
```

## Progress Tracking

- [✅] Phase 1: Create admin page and update navigation
- [✅] Phase 2: Refactor users page for students only
- [✅] Phase 3: API endpoints for admin management
- [✅] Phase 4: Implement bulk QR code download
- [ ] Phase 5: Extract shared components and optimize (HOLD)

## ✅ Implementation Checklist

### Phase 1: Admin Page & Navigation ✅

- [✅] Created `/app/admin/admins/page.tsx` - Complete admin management page
- [✅] Updated `AdminBottomNav` to include "Admin" tab for super_admin only
- [✅] Updated `AdminLayout` navigation to include admin management
- [✅] Added proper permission checks (super_admin only)
- [✅] Implemented admin table with search and role filtering
- [✅] Added create, edit, delete functionality for admins
- [✅] Added proper form validation and error handling

### Phase 2: Users Page Refactoring ✅

- [✅] Changed page title from "Manajemen User" to "Manajemen Siswa"
- [✅] Updated API calls to filter only students (`role === 'student'`)
- [✅] Removed role filter dropdown (no longer needed)
- [✅] Updated user statistics to show student-specific metrics
- [✅] Removed role column from table (all users are students)
- [✅] Updated form to default to student role only
- [✅] Removed admin-specific form fields
- [✅] Updated dialog titles and descriptions for students

### Phase 3: API Endpoints ✅

- [✅] Created `/api/admins/route.ts` - GET (list) and POST (create) endpoints
- [✅] Created `/api/admins/[id]/route.ts` - GET, PATCH, DELETE for individual admins
- [✅] Added proper authentication and authorization
- [✅] Implemented input validation with Zod schemas
- [✅] Added error handling and appropriate HTTP status codes
- [✅] Updated admin page to use new endpoints

### Code Quality & Best Practices ✅

- [✅] Followed existing code patterns and architecture
- [✅] Used TypeScript with proper type definitions
- [✅] Implemented proper error handling and user feedback
- [✅] Added loading states and form validation
- [✅] Maintained consistent UI/UX with existing pages
- [✅] No breaking changes to existing functionality
- [✅] Clean separation of concerns (students vs admins)

---

## 🔧 Bug Fixes Applied

### Issue: Admin Edit Functionality Error ✅

**Problem**: Error "User bukan admin" when editing super admin users
**Root Cause**:

1. API was using `userUseCases.getUserById()` which checks student repository first
2. Params handling needed to be updated for Next.js 15 compatibility

**Solution Applied**:

1. ✅ Updated admin API endpoints to use `adminRepo.findById()` directly
2. ✅ Fixed params handling to use `await params` pattern
3. ✅ Updated all CRUD operations to use correct repository methods
4. ✅ Fixed method calls: `updateAdmin()` instead of `updateUser()`

### Testing Checklist ✅

- [✅] Admin page loads correctly (`/admin/admins`)
- [✅] Navigation shows "Admin" tab for super_admin only
- [✅] Users page renamed to "Siswa" and filters students only
- [✅] API endpoints `/api/admins` work correctly
- [✅] Server compiles without errors
- [✅] No TypeScript diagnostics issues

### Phase 4: Bulk QR Code Download ✅

- [✅] Installed required dependencies (`jszip`, `qrcode`, `file-saver`, `@types/qrcode`)
- [✅] Created API endpoint `/api/students/bulk-qr-download/route.ts`
- [✅] Implemented server-side QR generation with proper error handling
- [✅] Added ZIP file creation with organized structure
- [✅] Created comprehensive UI dialog with three download options:
  - [✅] Download all students QR codes
  - [✅] Download selected students QR codes
  - [✅] Download QR codes by class
- [✅] Added real-time progress tracking with visual progress bar
- [✅] Implemented proper file naming with student names and unique codes
- [✅] Added README.txt file with usage instructions in ZIP
- [✅] Integrated with existing bulk operations UI pattern
- [✅] Added proper validation and error handling
- [✅] Tested build compilation successfully

### Technical Implementation Details ✅

**API Endpoint Features:**

- Server-side QR code generation using `qrcode` library
- ZIP file creation using `jszip` library
- Support for three download types: all, selected, class
- Proper authentication and authorization
- Error handling for missing students or invalid requests
- Safe filename generation with special character handling
- Automatic README.txt generation with metadata

**UI Features:**

- Clean dialog interface matching existing design patterns
- Radio button selection for download types
- Dynamic student count display for each option
- Class dropdown with student counts
- Real-time progress bar with status messages
- Proper form validation and disabled states
- Responsive design for mobile and desktop
- Toast notifications for success/error feedback

**File Structure:**

- QR codes saved as PNG files (300x300px)
- Filename format: `{student-name}-{unique-code-8chars}-{class-name}.png`
- ZIP filename includes timestamp and download type
- README.txt with generation metadata and instructions

### 🏗️ **REFACTORING: Clean Architecture Implementation** ✅

**Problem Solved:** The `app/admin/users/page.tsx` file had grown to 2870+ lines, violating clean architecture principles and making it difficult to maintain.

**Solution Implemented:**

- **Feature-Based Architecture:** Created `/features/bulk-qr-download/` directory
- **Separation of Concerns:** Split functionality into logical components
- **Custom Hooks:** Extracted business logic into `useQrDownload` hook
- **Reusable Components:** Created modular UI components
- **Type Safety:** Dedicated TypeScript type definitions

**File Structure Created:**

```
features/bulk-qr-download/
├── index.ts                    # Feature exports
├── types.ts                    # TypeScript definitions
├── hooks/
│   └── use-qr-download.ts     # Business logic hook
└── components/
    ├── qr-download-dialog.tsx  # Dialog component
    └── qr-download-button.tsx  # Button component
```

**Benefits Achieved:**

- ✅ **Reduced File Size:** Main page reduced by ~200 lines
- ✅ **Better Maintainability:** Each component has single responsibility
- ✅ **Reusability:** Components can be used in other pages
- ✅ **Testability:** Isolated logic easier to unit test
- ✅ **Type Safety:** Comprehensive TypeScript definitions
- ✅ **Clean Code:** Follows SOLID principles
- ✅ **Performance:** No impact on functionality or performance

**Technical Implementation:**

- **Custom Hook Pattern:** `useQrDownload` manages all state and business logic
- **Component Composition:** Dialog and button as separate, focused components
- **Props Interface:** Clean, typed interfaces for component communication
- **Error Handling:** Centralized error handling in custom hook
- **State Management:** Encapsulated state with clear actions

**Build Status:** ✅ Successful compilation with no errors

### 🛡️ **ERROR HANDLING IMPROVEMENTS** ✅

**Problem Solved:** Console errors when selecting classes with 0 students

**Improvements Implemented:**

- ✅ **Client-side Validation:** Pre-validate student counts before API calls
- ✅ **User-friendly Messages:** Clear, actionable error messages in Indonesian
- ✅ **Visual Feedback:** Disabled states and warning indicators
- ✅ **Graceful Degradation:** Proper handling of edge cases

**Specific Error Handling:**

1. **Empty Classes:**

   - Classes with 0 students are disabled in dropdown
   - Visual indicator shows "- Tidak ada siswa"
   - Warning message when all classes are empty

2. **No Students Selected:**

   - Clear validation message for selected students
   - Button disabled when no valid selection

3. **API Error Handling:**

   - Specific handling for 404 "No students found" errors
   - Graceful error recovery without console errors
   - User-friendly toast notifications

4. **Form Validation:**
   - Real-time validation of download options
   - Smart button disable/enable logic
   - Comprehensive edge case handling

**User Experience Improvements:**

- 🎯 **Proactive Validation:** Prevents invalid requests
- 🎯 **Clear Messaging:** Users understand what went wrong
- 🎯 **Visual Cues:** Disabled states guide user actions
- 🎯 **No Console Errors:** Clean development experience

**Last Updated:** December 2024
**Status:** COMPLETED - All functionality working correctly with Clean Architecture & Robust Error Handling ✅
