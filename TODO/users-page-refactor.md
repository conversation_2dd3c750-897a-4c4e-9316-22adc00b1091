# TODO: Student Management Page Refactor & Gender/NIS Support

## 1. Refactor for Clean Code & Clean Architecture

- [x] Table component (`StudentTable`)
- [x] Filter/search bar component (`StudentFilterBar`)
- [x] Add/Edit dialog component (`StudentDialog`)
- [ ] Bulk upload dialog component (`BulkUploadDialog`)
- [ ] Bulk class update dialog component
- [ ] Bulk delete dialog component
- [ ] Stats cards component
- [ ] Pagination component
- [ ] Custom hooks for data fetching, filtering, and pagination
- [ ] Move utility functions (sorting, filtering, etc.) to hooks/utils.
- [ ] Ensure all props/types are typed and reusable.
- [ ] Keep main page file <300 lines, only orchestration.

## 2. Show Gender & NIS in Table

- [x] Add "Gender" and "NIS" columns to the student table.
- [x] Show gender as "Laki-laki" or "Perempuan" in the UI.
- [x] Add filter dropdown for gender (all/laki-laki/perempuan).
- [x] Add filter/search for NIS.

## 3. Adjust Search

- [x] Extend search to include gender (both "laki-laki"/"perempuan" and "male"/"female") and NIS.

## 4. Adjust Add User & Bulk User

- [x] Add gender select (required) to add user dialog.
- [x] Add gender column to bulk upload CSV (with validation: only "Laki-laki"/"Perempuan" or "male"/"female" accepted).
- [x] Update CSV sample and validation logic.
- [x] Pass gender to API on add/bulk user.

## 5. Adjust Download QR with Gender

- [x] Ensure gender is available in the QR download dialog (for preview/info).
- [x] Optionally, add gender to the QR filename or export data if needed.
- [x] Update QR download types and hooks to include gender.

## 6. General

- [x] Update all relevant types/interfaces for gender/NIS (for bulk upload)
- [x] Test all flows: add, edit, bulk upload, filter, search, QR download.
- [x] Update documentation if needed.

---

## 7. Post-Release Manual Testing Fixes (Current Focus)

- [ ] Fix gender not showing on table
- [ ] Fix gender filter not working
- [ ] Remove search for gender
- [ ] Adjust bulk upload for gender (wording, data, CSV example, logic, API, UI)
- [ ] Add gender filter to QR download dialog
- [ ] Add cards for total Laki-laki and Perempuan

**Current focus: Addressing post-release manual testing issues for gender support.**
