import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Edit, Trash2 } from 'lucide-react'

interface User {
  id: number
  name: string
  role: 'student' | 'admin' | 'super_admin'
  uniqueCode?: string
  username?: string
  classId?: number
  className?: string
  nis?: string
  gender?: 'male' | 'female'
}

interface StudentTableProps {
  users: User[]
  paginatedUsers: User[]
  selectedUsers: Set<number>
  currentPage: number
  itemsPerPage: number
  isSubmitting: boolean
  isLoading: boolean
  isCheckingAttendance: boolean
  handleEditUser: (user: User) => void
  handleDeleteClick: (user: User) => void
  toggleUserSelection: (userId: number) => void
  toggleAllUsers: () => void
  handleSort: (key: string) => void
  renderSortIndicator: (key: string) => React.ReactNode
}

const genderLabel = (gender?: 'male' | 'female' | string) => {
  if (gender === 'male') return 'Laki-laki'
  if (gender === 'female') return 'Perempuan'
  if (typeof gender === 'string' && gender.trim() !== '') return gender
  return <span className="text-red-500">(Tidak ada data)</span>
}

export const StudentTable: React.FC<StudentTableProps> = ({
  users,
  paginatedUsers,
  selectedUsers,
  currentPage,
  itemsPerPage,
  isSubmitting,
  isLoading,
  isCheckingAttendance,
  handleEditUser,
  handleDeleteClick,
  toggleUserSelection,
  toggleAllUsers,
  handleSort,
  renderSortIndicator,
}) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px]">
            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                checked={paginatedUsers.length > 0 && selectedUsers.size === paginatedUsers.length}
                onChange={toggleAllUsers}
                title={
                  selectedUsers.size === paginatedUsers.length
                    ? 'Batalkan semua seleksi'
                    : 'Pilih semua pengguna di halaman ini'
                }
              />
            </div>
          </TableHead>
          <TableHead className="w-[50px]">No</TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('identifier')}
          >
            ID/Kode Unik {renderSortIndicator('identifier')}
          </TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('username')}
          >
            Username {renderSortIndicator('username')}
          </TableHead>
          <TableHead className="cursor-pointer hover:bg-gray-50" onClick={() => handleSort('name')}>
            Nama {renderSortIndicator('name')}
          </TableHead>
          <TableHead className="cursor-pointer hover:bg-gray-50" onClick={() => handleSort('nis')}>
            NIS {renderSortIndicator('nis')}
          </TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('gender')}
          >
            Gender {renderSortIndicator('gender')}
          </TableHead>
          {/* DEBUG: Show raw gender value for troubleshooting */}
          {/* <TableHead>Raw Gender</TableHead> */}
          <TableHead
            className="cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('className')}
          >
            Kelas {renderSortIndicator('className')}
          </TableHead>
          <TableHead className="text-right">Aksi</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.length === 0 ? (
          <TableRow>
            <TableCell colSpan={10} className="h-24 text-center">
              Tidak ada data pengguna
            </TableCell>
          </TableRow>
        ) : paginatedUsers.length === 0 ? (
          <TableRow>
            <TableCell colSpan={10} className="h-24 text-center">
              Tidak ada hasil yang cocok dengan pencarian
            </TableCell>
          </TableRow>
        ) : (
          paginatedUsers.map((user, index) => (
            <TableRow
              key={user.id}
              className={`hover:bg-indigo-50 dark:hover:bg-slate-800 ${
                selectedUsers.has(user.id) ? 'bg-indigo-50 dark:bg-slate-800/60' : ''
              }`}
            >
              <TableCell>
                <input
                  type="checkbox"
                  className="h-5 w-5 cursor-pointer rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  checked={selectedUsers.has(user.id)}
                  onChange={() => toggleUserSelection(user.id)}
                  title="Pilih/batalkan pilih pengguna ini"
                />
              </TableCell>
              <TableCell className="text-center font-medium">
                {(currentPage - 1) * itemsPerPage + index + 1}
              </TableCell>
              <TableCell>
                {user.uniqueCode ? `${user.uniqueCode.substring(0, 8)}...` : user.username || '-'}
              </TableCell>
              <TableCell>{user.username || '-'}</TableCell>
              <TableCell>{user.name}</TableCell>
              <TableCell>{user.nis || '-'}</TableCell>
              <TableCell>{genderLabel(user.gender)}</TableCell>
              {/* <TableCell>{String(user.gender)}</TableCell> */}
              <TableCell>{user.className || '-'}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditUser(user)}
                    disabled={isSubmitting || isLoading}
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                    onClick={() => handleDeleteClick(user)}
                    disabled={isSubmitting || isCheckingAttendance || isLoading}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
