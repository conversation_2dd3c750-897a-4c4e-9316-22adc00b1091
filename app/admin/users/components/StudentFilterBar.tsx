import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, X } from 'lucide-react'

interface Class {
  id: number
  name: string
}

interface StudentFilterBarProps {
  searchQuery: string
  setSearchQuery: (q: string) => void
  classFilter: string
  setClassFilter: (c: string) => void
  genderFilter: string
  setGenderFilter: (g: string) => void
  classes: Class[]
}

export const StudentFilterBar: React.FC<StudentFilterBarProps> = ({
  searchQuery,
  setSearchQuery,
  classFilter,
  setClassFilter,
  genderFilter,
  setGenderFilter,
  classes,
}) => {
  return (
    <div className="flex flex-1 flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
        <Input
          placeholder="Cari nama, username, kode, NIS, atau kelas..."
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className="pl-10"
        />
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      <Select value={classFilter} onValueChange={setClassFilter}>
        <SelectTrigger className="w-full md:w-[180px]">
          <SelectValue placeholder="Filter kelas" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Kelas</SelectItem>
          {classes.map(cls => (
            <SelectItem key={cls.id} value={String(cls.id)}>
              {cls.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Select value={genderFilter} onValueChange={setGenderFilter}>
        <SelectTrigger className="w-full md:w-[150px]">
          <SelectValue placeholder="Filter gender" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Gender</SelectItem>
          <SelectItem value="male">Laki-laki</SelectItem>
          <SelectItem value="female">Perempuan</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
