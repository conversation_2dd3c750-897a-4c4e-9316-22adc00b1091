import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PasswordInput } from '@/components/ui/password-input'
import { Loader2, AlertCircle, Plus, Edit, Shield, ShieldCheck, User2 } from 'lucide-react'

interface Class {
  id: number
  name: string
}

interface User {
  id: number
  name: string
  role: 'student' | 'admin' | 'super_admin'
  uniqueCode?: string
  username?: string
  classId?: number
  className?: string
  gender?: 'male' | 'female'
}

interface FormData {
  id: string
  name: string
  role: string
  password: string
  classId: string
  gender: string
}

interface FormErrors {
  password?: string
}

type DialogMode = 'add' | 'edit'

interface StudentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  dialogMode: DialogMode
  formData: FormData
  setFormData: (data: FormData) => void
  formErrors: FormErrors
  isSubmitting: boolean
  classes: Class[]
  isLoadingClasses: boolean
  onSubmit: (e: React.FormEvent) => void
  onCancel: () => void
  selectedUser?: User | null
}

export const StudentDialog: React.FC<StudentDialogProps> = ({
  open,
  onOpenChange,
  dialogMode,
  formData,
  setFormData,
  formErrors,
  isSubmitting,
  classes,
  isLoadingClasses,
  onSubmit,
  onCancel,
  selectedUser,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {dialogMode === 'add' ? (
              <>
                <Plus className="mr-2 h-5 w-5 text-indigo-600" />
                Tambah Siswa Baru
              </>
            ) : (
              <>
                <Edit className="mr-2 h-5 w-5 text-indigo-600" />
                Edit Siswa
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {dialogMode === 'add'
              ? 'Masukkan informasi untuk siswa baru'
              : 'Perbarui informasi siswa'}
          </DialogDescription>
        </DialogHeader>

        {/* Role indicator for edit mode */}
        {dialogMode === 'edit' && selectedUser && (
          <div className="mb-4 flex items-center rounded-md bg-slate-50 p-3 dark:bg-slate-800/50">
            <div
              className={`mr-3 rounded-full p-2 ${
                selectedUser.role === 'super_admin'
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                  : selectedUser.role === 'admin'
                    ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
              }`}
            >
              {selectedUser.role === 'super_admin' ? (
                <Shield className="h-5 w-5" />
              ) : selectedUser.role === 'admin' ? (
                <ShieldCheck className="h-5 w-5" />
              ) : (
                <User2 className="h-5 w-5" />
              )}
            </div>
            <div>
              <p className="font-medium text-slate-800 dark:text-slate-200">{selectedUser.name}</p>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {selectedUser.role === 'student'
                  ? `Siswa ${selectedUser.className ? `- ${selectedUser.className}` : ''}`
                  : selectedUser.role === 'super_admin'
                    ? 'Super Admin'
                    : 'Admin'}
              </p>
            </div>
          </div>
        )}

        <form onSubmit={onSubmit} className="space-y-4">
          {/* Role is fixed to student for this page */}
          {dialogMode === 'add' && (
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <div className="flex items-center rounded-md bg-blue-50 p-3 dark:bg-blue-900/30">
                <User2 className="mr-2 h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Siswa (Student)
                </span>
              </div>
              <p className="text-xs text-slate-500">
                Halaman ini khusus untuk mengelola data siswa
              </p>
            </div>
          )}

          {/* Auto-generated unique code info for students */}
          {dialogMode === 'add' && (
            <div className="mb-2 text-xs text-slate-500">
              Kode unik untuk siswa akan dibuat otomatis
            </div>
          )}

          {/* Username & Password untuk siswa */}
          {dialogMode === 'add' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={formData.id}
                  onChange={e => setFormData({ ...formData, id: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                {formErrors.password && (
                  <div className="mb-2 flex items-center text-sm text-red-500">
                    <AlertCircle className="mr-1 h-4 w-4" />
                    {formErrors.password}
                  </div>
                )}
                <PasswordInput
                  id="password"
                  value={formData.password}
                  onChange={e => setFormData({ ...formData, password: e.target.value })}
                  required
                  className={formErrors.password ? 'border-red-500' : ''}
                />
                <p className="text-xs text-slate-500">Password harus minimal 6 karakter</p>
              </div>
            </>
          )}

          {/* Name field */}
          <div className="space-y-2">
            <Label htmlFor="name">Nama</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={e => setFormData({ ...formData, name: e.target.value })}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Gender select */}
          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select
              value={formData.gender}
              onValueChange={value => setFormData({ ...formData, gender: value })}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Laki-laki</SelectItem>
                <SelectItem value="female">Perempuan</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-slate-500">Pilih gender siswa</p>
          </div>

          {/* Class selection for students */}
          <div className="space-y-2">
            <Label htmlFor="classId">Kelas</Label>
            {isLoadingClasses ? (
              <div className="flex items-center space-x-2 py-2">
                <Loader2 className="h-4 w-4 animate-spin text-indigo-600" />
                <span className="text-sm text-slate-500">Memuat data kelas...</span>
              </div>
            ) : classes.length > 0 ? (
              <Select
                value={formData.classId}
                onValueChange={value => setFormData({ ...formData, classId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kelas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Tidak Ada Kelas</SelectItem>
                  {classes.map(cls => (
                    <SelectItem key={cls.id} value={String(cls.id)}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="mb-2 mt-1 text-sm text-amber-600 dark:text-amber-400">
                <p>Tidak ada kelas tersedia. Silakan tambahkan kelas terlebih dahulu.</p>
                <Button
                  type="button"
                  variant="link"
                  className="h-auto p-0 text-indigo-600 dark:text-indigo-400"
                  onClick={() => {
                    onOpenChange(false)
                    window.location.href = '/admin/classes'
                  }}
                >
                  Pergi ke Manajemen Kelas
                </Button>
              </div>
            )}
            <p className="text-xs text-slate-500">
              Kelas akan digunakan untuk mengelompokkan siswa
            </p>
          </div>

          {/* Password field for editing students (Super Admin only) */}
          {dialogMode === 'edit' && (
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              {formErrors.password && (
                <div className="mb-2 flex items-center text-sm text-red-500">
                  <AlertCircle className="mr-1 h-4 w-4" />
                  {formErrors.password}
                </div>
              )}
              <PasswordInput
                id="password"
                value={formData.password}
                onChange={e => setFormData({ ...formData, password: e.target.value })}
                placeholder="Kosongkan jika tidak ingin mengubah password"
                className={formErrors.password ? 'border-red-500' : ''}
              />
              <p className="text-xs text-slate-500">
                Kosongkan jika tidak ingin mengubah password. Password minimal 6 karakter.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Batal
            </Button>
            <Button
              type="submit"
              className="bg-indigo-600 text-white hover:bg-indigo-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {dialogMode === 'add' ? 'Menambahkan...' : 'Menyimpan...'}
                </>
              ) : dialogMode === 'add' ? (
                'Tambah'
              ) : (
                'Simpan'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
