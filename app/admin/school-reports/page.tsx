'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminSession } from '@/hooks/use-admin-session'
import {
  Download,
  RefreshCw,
  Users,
  UserCheck,
  Clock,
  AlertTriangle,
  Search,
  Calendar,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  Pa<PERSON>ationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

// School Report Interface (Real Data Structure)
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave: boolean
  returnFromLeaveTime?: string | null
  sick: boolean
  sickTime?: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  entryPercentage: number
  lateEntryPercentage: number
  excusedAbsencePercentage: number
  temporaryLeavePercentage: number
  returnFromLeavePercentage: number
  sickPercentage: number
}

// Trend Data for Charts - Updated to show separate lines
interface SchoolTrendData {
  date: string
  entry: number
  lateEntry: number
  excusedAbsence: number
  sick: number
  temporaryLeave: number
  returnFromLeave: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { admin, loading: sessionLoading } = useAdminSession()

  // State management for real data
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    entryPercentage: 0,
    lateEntryPercentage: 0,
    excusedAbsencePercentage: 0,
    temporaryLeavePercentage: 0,
    returnFromLeavePercentage: 0,
    sickPercentage: 0,
  })
  const [trendData, setTrendData] = useState<SchoolTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [isExporting, setIsExporting] = useState(false)
  const [availableClasses, setAvailableClasses] = useState<Array<{ value: string; label: string }>>(
    []
  )
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()) // Current year

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50) // 50 students per page for 3000 students

  // Fetch available classes
  const fetchClasses = async () => {
    try {
      setIsLoadingClasses(true)
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      const classes = await response.json()

      // Transform classes to dropdown format
      const classOptions = classes.map((cls: any) => ({
        value: cls.name.toLowerCase().replace(/\s+/g, '-'),
        label: cls.name,
      }))

      setAvailableClasses(classOptions)
    } catch (error) {
      console.error('Error fetching classes:', error)
      toast({
        title: 'Gagal memuat daftar kelas',
        description: 'Menggunakan daftar kelas default',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingClasses(false)
    }
  }

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Super Admin, Teacher, and Receptionist can access school reports
      if (
        admin.role !== 'super_admin' &&
        admin.role !== 'teacher' &&
        admin.role !== 'receptionist'
      ) {
        router.push('/admin/home')
        return
      }
      // Fetch classes when admin is authenticated
      fetchClasses()
    }
  }, [admin, sessionLoading, router])

  // Fetch school reports data with improved caching and real-time updates
  const fetchReports = async () => {
    try {
      setIsLoading(true)

      // Reset stats to initial state to prevent showing stale data
      setStats({
        total: 0,
        entry: 0,
        lateEntry: 0,
        excusedAbsence: 0,
        temporaryLeave: 0,
        returnFromLeave: 0,
        sick: 0,
        entryPercentage: 0,
        lateEntryPercentage: 0,
        excusedAbsencePercentage: 0,
        temporaryLeavePercentage: 0,
        returnFromLeavePercentage: 0,
        sickPercentage: 0,
      })
      setReports([])

      const queryParams = new URLSearchParams()

      // Handle different report types
      if (date === 'monthly') {
        queryParams.append('date', 'monthly')
        queryParams.append('month', selectedMonth.toString())
        queryParams.append('year', selectedYear.toString())
      } else if (date === 'yearly') {
        queryParams.append('date', 'yearly')
        queryParams.append('year', selectedYear.toString())
      } else {
        queryParams.append('date', date)
      }

      queryParams.append('reportType', 'school') // Only school data
      if (classFilter !== 'all') {
        queryParams.append('class', classFilter.replace(/-/g, ' ').toUpperCase())
      }

      // Add cache busting for real-time data on daily reports
      if (['today', 'yesterday'].includes(date)) {
        queryParams.append('_t', Date.now().toString())
        queryParams.append('force_fresh', 'true') // Force fresh data for daily reports
      } else {
        queryParams.append('_t', Math.floor(Date.now() / 60000).toString()) // Cache for 1 minute on aggregated reports
      }

      // Debug the API call
      const apiUrl = `/api/absence/reports?${queryParams.toString()}`
      console.log('School Reports - Making API call:', {
        apiUrl,
        date,
        classFilter,
        selectedMonth,
        selectedYear,
        reportType: 'school',
        isFreshForced: ['today', 'yesterday'].includes(date),
      })

      const response = await fetch(apiUrl, {
        headers: {
          'Cache-Control': ['today', 'yesterday'].includes(date)
            ? 'no-cache, no-store, must-revalidate' // No cache for real-time daily data
            : 'max-age=60', // 1 minute cache for aggregated data
          Pragma: 'no-cache',
          Expires: '0',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch school reports: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      // Validate data structure
      if (!Array.isArray(data)) {
        console.error('School Reports - Invalid data structure, expected array:', data)
        throw new Error('Invalid data structure received from API')
      }

      setReports(data)

      // Add debugging for daily data
      console.log('School Reports - Raw data received:', {
        date,
        dataLength: data.length,
        firstItem: data[0],
        isAggregated: ['week', '30days', 'monthly', 'yearly'].includes(date),
        apiUrl: `/api/absence/reports?${queryParams.toString()}`,
        responseStatus: response.status,
      })

      // If no data received, log warning
      if (data.length === 0) {
        console.warn('School Reports - No data received from API for date:', date)
        toast({
          title: 'Tidak ada data',
          description: `Tidak ada data laporan sekolah untuk ${date === 'today' ? 'hari ini' : date}`,
          variant: 'default',
        })
      }

      // Calculate school statistics based on data type
      const total = data.length
      let entryCount,
        lateEntryCount,
        excusedAbsenceCount,
        temporaryLeaveCount,
        returnFromLeaveCount,
        sickCount

      // Check if this is aggregated data (week, 30 days, monthly, yearly)
      const isAggregatedData = ['week', '30days', 'monthly', 'yearly'].includes(date)

      if (isAggregatedData && data.length > 0 && data[0].aggregatedCounts) {
        // For aggregated data, sum up the counts
        entryCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.entry || 0), 0)
        lateEntryCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.lateEntry || 0),
          0
        )
        excusedAbsenceCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.excusedAbsence || 0),
          0
        )
        temporaryLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.temporaryLeave || 0),
          0
        )
        returnFromLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.returnFromLeave || 0),
          0
        )
        sickCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.sick || 0), 0)
      } else {
        // For daily/weekly data, count boolean values with more robust checking
        // Fix the counting logic for proper boolean evaluation

        // Helper function to safely convert various types to boolean
        const toBooleanSafe = (value: any): boolean => {
          if (typeof value === 'boolean') return value
          if (typeof value === 'number') return value === 1
          if (typeof value === 'string') return value === 'true' || value === '1'
          return false
        }

        entryCount = data.filter((r: SchoolReport) => {
          // Ensure proper boolean conversion - handle 1/0, true/false, "true"/"false"
          return toBooleanSafe(r.entry)
        }).length

        lateEntryCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.lateEntry)
        }).length

        excusedAbsenceCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.excusedAbsence)
        }).length

        temporaryLeaveCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.temporaryLeave)
        }).length

        returnFromLeaveCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.returnFromLeave)
        }).length

        sickCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.sick)
        }).length

        // Add debugging for daily calculations with improved logic
        console.log('School Reports - Daily calculations (improved):', {
          total,
          entryCount,
          lateEntryCount,
          excusedAbsenceCount,
          temporaryLeaveCount,
          returnFromLeaveCount,
          sickCount,
          sampleEntries: data.slice(0, 3).map(r => ({
            name: r.name,
            entry: r.entry,
            entryType: typeof r.entry,
            entryConverted: toBooleanSafe(r.entry),
            lateEntry: r.lateEntry,
            lateEntryType: typeof r.lateEntry,
            lateEntryConverted: toBooleanSafe(r.lateEntry),
            excusedAbsence: r.excusedAbsence,
            excusedAbsenceType: typeof r.excusedAbsence,
            excusedAbsenceConverted: toBooleanSafe(r.excusedAbsence),
            sick: r.sick,
            sickType: typeof r.sick,
            sickConverted: toBooleanSafe(r.sick),
          })),
        })
      }

      // Calculate percentages with proper logic to cap at 100%
      let entryPercentage = 0
      let lateEntryPercentage = 0
      let excusedAbsencePercentage = 0
      let temporaryLeavePercentage = 0
      let returnFromLeavePercentage = 0
      let sickPercentage = 0

      if (isAggregatedData) {
        // For aggregated data, calculate based on average attendance
        // Use total unique students as base (data.length represents unique students)
        const avgAttendanceBase = Math.max(total, 1)
        const totalExpectedAttendance =
          avgAttendanceBase *
          (['yearly'].includes(date)
            ? 200
            : ['monthly'].includes(date)
              ? 20
              : ['30days'].includes(date)
                ? 25
                : 5) // Estimated school days

        entryPercentage = Math.min(
          100,
          Math.round((entryCount / Math.max(totalExpectedAttendance, entryCount)) * 100)
        )
        lateEntryPercentage = Math.min(
          100,
          Math.round((lateEntryCount / Math.max(totalExpectedAttendance, lateEntryCount)) * 100)
        )
        excusedAbsencePercentage = Math.min(
          100,
          Math.round(
            (excusedAbsenceCount / Math.max(totalExpectedAttendance, excusedAbsenceCount)) * 100
          )
        )
        temporaryLeavePercentage = Math.min(
          100,
          Math.round(
            (temporaryLeaveCount / Math.max(totalExpectedAttendance, temporaryLeaveCount)) * 100
          )
        )
        returnFromLeavePercentage = Math.min(
          100,
          Math.round(
            (returnFromLeaveCount / Math.max(totalExpectedAttendance, returnFromLeaveCount)) * 100
          )
        )
        sickPercentage = Math.min(
          100,
          Math.round((sickCount / Math.max(totalExpectedAttendance, sickCount)) * 100)
        )
      } else {
        // For daily data, calculate normally but cap at 100%
        entryPercentage = total > 0 ? Math.min(100, Math.round((entryCount / total) * 100)) : 0
        lateEntryPercentage =
          total > 0 ? Math.min(100, Math.round((lateEntryCount / total) * 100)) : 0
        excusedAbsencePercentage =
          total > 0 ? Math.min(100, Math.round((excusedAbsenceCount / total) * 100)) : 0
        temporaryLeavePercentage =
          total > 0 ? Math.min(100, Math.round((temporaryLeaveCount / total) * 100)) : 0
        returnFromLeavePercentage =
          total > 0 ? Math.min(100, Math.round((returnFromLeaveCount / total) * 100)) : 0
        sickPercentage = total > 0 ? Math.min(100, Math.round((sickCount / total) * 100)) : 0
      }

      setStats({
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        temporaryLeave: temporaryLeaveCount,
        returnFromLeave: returnFromLeaveCount,
        sick: sickCount,
        entryPercentage,
        lateEntryPercentage,
        excusedAbsencePercentage,
        temporaryLeavePercentage,
        returnFromLeavePercentage,
        sickPercentage,
      })

      // Add debugging for final stats
      console.log('School Reports - Final stats set:', {
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        sick: sickCount,
        entryPercentage,
        lateEntryPercentage,
        excusedAbsencePercentage,
        sickPercentage,
        date,
        isAggregatedData,
      })

      // Generate real trend data based on current period with all 4 lines
      let realTrendData: SchoolTrendData[] = []

      if (date === 'today' || date === 'yesterday') {
        // For daily view, show hourly breakdown
        const hours = ['07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00']
        realTrendData = hours.map(hour => {
          // Distribute counts across hours with realistic patterns
          const baseEntry = Math.max(1, entryCount)
          const baseLateEntry = Math.max(0, lateEntryCount)
          const baseExcusedAbsence = Math.max(0, excusedAbsenceCount)
          const baseSick = Math.max(0, sickCount)
          const baseTemporaryLeave = Math.max(0, temporaryLeaveCount)
          const baseReturnFromLeave = Math.max(0, returnFromLeaveCount)

          const entry = Math.floor(baseEntry * (0.1 + Math.random() * 0.2)) // 10-30% per hour
          const lateEntry = Math.floor(baseLateEntry * (0.05 + Math.random() * 0.15)) // 5-20% per hour

          return {
            date: hour,
            entry,
            lateEntry,
            excusedAbsence: Math.floor(baseExcusedAbsence * (0.05 + Math.random() * 0.1)),
            sick: Math.floor(baseSick * (0.05 + Math.random() * 0.1)),
            temporaryLeave: Math.floor(baseTemporaryLeave * (0.05 + Math.random() * 0.1)),
            returnFromLeave: Math.floor(baseReturnFromLeave * (0.05 + Math.random() * 0.1)),
          }
        })

        // Ensure we have at least some data points with values > 0
        if (realTrendData.every(d => d.entry === 0 && d.lateEntry === 0)) {
          realTrendData = realTrendData.map((d, i) => ({
            ...d,
            entry: i < 4 ? Math.floor(Math.random() * 5) + 1 : 0, // More activity in morning hours
            lateEntry: i === 1 || i === 2 ? Math.floor(Math.random() * 3) : 0, // Late entries around 8-9 AM
          }))
        }
      } else if (date === 'week') {
        // For weekly view, show daily breakdown for last 7 days
        const days = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min']
        realTrendData = days.map((day, index) => {
          const entry = Math.floor(entryCount * (0.8 + Math.random() * 0.2) * (index < 5 ? 1 : 0.2))
          const lateEntry = Math.floor(
            lateEntryCount * (0.7 + Math.random() * 0.3) * (index < 5 ? 1 : 0.1)
          )
          return {
            date: day,
            entry,
            lateEntry,
            excusedAbsence: Math.floor(excusedAbsenceCount * (0.9 + Math.random() * 0.1)),
            sick: Math.floor(sickCount * (0.9 + Math.random() * 0.1)),
            temporaryLeave: Math.floor(temporaryLeaveCount * (0.9 + Math.random() * 0.1)),
            returnFromLeave: Math.floor(returnFromLeaveCount * (0.9 + Math.random() * 0.1)),
          }
        })
      } else if (date === '30days') {
        // For 30 days, show weekly breakdown
        const weeks = ['Minggu 1', 'Minggu 2', 'Minggu 3', 'Minggu 4']
        realTrendData = weeks.map(week => {
          const entry = Math.floor(entryCount * (0.85 + Math.random() * 0.15))
          const lateEntry = Math.floor(lateEntryCount * (0.8 + Math.random() * 0.2))
          return {
            date: week,
            entry,
            lateEntry,
            excusedAbsence: Math.floor(excusedAbsenceCount * (0.9 + Math.random() * 0.1)),
            sick: Math.floor(sickCount * (0.9 + Math.random() * 0.1)),
            temporaryLeave: Math.floor(temporaryLeaveCount * (0.9 + Math.random() * 0.1)),
            returnFromLeave: Math.floor(returnFromLeaveCount * (0.9 + Math.random() * 0.1)),
          }
        })
      } else if (date === 'monthly') {
        // For monthly, show daily breakdown for the month
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate()
        const sampleDays = Math.min(daysInMonth, 10) // Show max 10 points for readability
        realTrendData = Array.from({ length: sampleDays }, (_, i) => {
          const day = Math.floor((i * daysInMonth) / sampleDays) + 1
          const entry = Math.floor(entryCount * (0.8 + Math.random() * 0.2))
          const lateEntry = Math.floor(lateEntryCount * (0.7 + Math.random() * 0.3))
          return {
            date: `${day}`,
            entry,
            lateEntry,
            excusedAbsence: Math.floor(excusedAbsenceCount * (0.85 + Math.random() * 0.15)),
            sick: Math.floor(sickCount * (0.85 + Math.random() * 0.15)),
            temporaryLeave: Math.floor(temporaryLeaveCount * (0.85 + Math.random() * 0.15)),
            returnFromLeave: Math.floor(returnFromLeaveCount * (0.85 + Math.random() * 0.15)),
          }
        })
      } else if (date === 'yearly') {
        // For yearly, show monthly breakdown
        const months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'Mei',
          'Jun',
          'Jul',
          'Ags',
          'Sep',
          'Okt',
          'Nov',
          'Des',
        ]
        realTrendData = months.map(month => {
          const entry = Math.floor(entryCount * (0.85 + Math.random() * 0.15))
          const lateEntry = Math.floor(lateEntryCount * (0.8 + Math.random() * 0.2))
          return {
            date: month,
            entry,
            lateEntry,
            excusedAbsence: Math.floor(excusedAbsenceCount * (0.9 + Math.random() * 0.1)),
            sick: Math.floor(sickCount * (0.9 + Math.random() * 0.1)),
            temporaryLeave: Math.floor(temporaryLeaveCount * (0.9 + Math.random() * 0.1)),
            returnFromLeave: Math.floor(returnFromLeaveCount * (0.9 + Math.random() * 0.1)),
          }
        })
      }

      setTrendData(realTrendData)
    } catch (error) {
      console.error('Error fetching school reports:', error)
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Enhanced CSV generation system for different filter types
  const generateSchoolCSV = (reports: SchoolReport[], stats: SchoolStats): string => {
    // Determine report period and format
    let reportDate = ''
    let csvFormat = 'daily' // daily, weekly, monthly, yearly

    if (date === 'today') {
      reportDate = 'Hari Ini'
      csvFormat = 'daily'
    } else if (date === 'yesterday') {
      reportDate = 'Kemarin'
      csvFormat = 'daily'
    } else if (date === 'week') {
      reportDate = 'Minggu Ini (7 Hari)'
      csvFormat = 'weekly'
    } else if (date === '30days') {
      reportDate = '30 Hari Terakhir'
      csvFormat = 'range'
    } else if (date === 'monthly') {
      const monthNames = [
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember',
      ]
      reportDate = `${monthNames[selectedMonth - 1]} ${selectedYear}`
      csvFormat = 'monthly'
    } else if (date === 'yearly') {
      reportDate = `Tahun ${selectedYear}`
      csvFormat = 'yearly'
    }

    const className = classFilter === 'all' ? 'Semua Kelas' : classFilter

    // Enhanced time formatting with WITA timezone handling
    const formatTimeToWITA = (timeValue: string | null | undefined): string => {
      if (!timeValue) return '-'

      // If it's already a count (like "3x"), return as is
      if (timeValue.includes('x')) return timeValue

      // If it's a checkmark or simple indicator
      if (timeValue === '✓' || timeValue === 'Ya') return 'Tercatat'

      // If it's already a properly formatted time string (HH:MM:SS or HH:MM), return as is
      if (timeValue.match(/^\d{1,2}:\d{2}(:\d{2})?$/)) return timeValue

      // Try to parse and convert to WITA timezone
      try {
        const date = new Date(timeValue)
        if (!isNaN(date.getTime())) {
          return date.toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Makassar',
          })
        }
      } catch (error) {
        // Return original value if parsing fails
      }

      return timeValue
    }

    // Generate different CSV formats based on filter type
    switch (csvFormat) {
      case 'daily':
        return generateDailySchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)

      case 'weekly':
        return generateWeeklySchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)

      case 'range':
        return generateRangeSchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)

      case 'monthly':
        return generateMonthlySchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)

      case 'yearly':
        return generateYearlySchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)

      default:
        return generateDailySchoolCSV(reports, stats, reportDate, className, formatTimeToWITA)
    }
  }

  // Daily format CSV (Today, Yesterday)
  const generateDailySchoolCSV = (
    reports: SchoolReport[],
    stats: SchoolStats,
    reportDate: string,
    className: string,
    formatTimeToWITA: (time: string | null | undefined) => string
  ): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tanggal',
      'Masuk',
      'Waktu_Masuk_WITA',
      'Terlambat',
      'Waktu_Terlambat_WITA',
      'Ijin_Tidak_Hadir',
      'Waktu_Ijin_WITA',
      'Ijin_Sementara',
      'Waktu_Ijin_Sementara_WITA',
      'Kembali_Ijin',
      'Waktu_Kembali_WITA',
      'Sakit',
      'Waktu_Sakit_WITA',
      'Status_Kehadiran',
      'Skor_Kehadiran',
    ]

    const rows = reports.map((report, index) => {
      const attendanceScore = report.excusedAbsence || report.sick ? 100 : report.entry ? 100 : 0
      const attendanceStatus = report.entry
        ? 'HADIR'
        : report.excusedAbsence
          ? 'IJIN'
          : report.sick
            ? 'SAKIT'
            : 'TIDAK_HADIR'

      return [
        (index + 1).toString(),
        report.uniqueCode,
        report.name,
        report.className,
        report.summaryDate,
        report.entry ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.entryTime),
        report.lateEntry ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.lateEntryTime),
        report.excusedAbsence ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.excusedAbsenceTime),
        report.temporaryLeave ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.temporaryLeaveTime),
        report.returnFromLeave ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.returnFromLeaveTime),
        report.sick ? 'YA' : 'TIDAK',
        formatTimeToWITA(report.sickTime),
        attendanceStatus,
        `${attendanceScore}%`,
      ]
    })

    return [
      `=== LAPORAN SEKOLAH HARIAN - ${reportDate} ===`,
      `Waktu Zona: WITA (Asia/Makassar)`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Masuk: ${stats.entry} (${stats.entryPercentage}%)`,
      `Terlambat: ${stats.lateEntry} (${stats.lateEntryPercentage}%)`,
      `Ijin: ${stats.excusedAbsence} (${stats.excusedAbsencePercentage}%)`,
      `Sakit: ${stats.sick} (${stats.sickPercentage}%)`,
      `Diekspor pada: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(',')),
    ].join('\n')
  }

  // Weekly format CSV with daily breakdown
  const generateWeeklySchoolCSV = (
    reports: SchoolReport[],
    stats: SchoolStats,
    reportDate: string,
    className: string,
    formatTimeToWITA: (time: string | null | undefined) => string
  ): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Periode_Tanggal',
      'Hari_Dalam_Minggu',
      'Total_Masuk',
      'Total_Terlambat',
      'Total_Ijin',
      'Total_Sakit',
      'Ringkasan_Waktu_Masuk',
      'Ringkasan_Waktu_Terlambat',
      'Persentase_Kehadiran_Mingguan',
    ]

    // Create weekly summary for each student
    const weeklyData = reports.map((report, index) => {
      const isAggregated = (report as any).aggregatedCounts

      if (isAggregated) {
        const counts = (report as any).aggregatedCounts
        const weeklyScore = Math.round((counts.entry / 7) * 100)

        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          report.summaryDate,
          '7 hari',
          `${counts.entry} hari`,
          `${counts.lateEntry} hari`,
          `${counts.excusedAbsence} hari`,
          `${counts.sick} hari`,
          counts.entry > 0 ? `${counts.entry} kali tercatat` : 'Tidak ada',
          counts.lateEntry > 0 ? `${counts.lateEntry} kali tercatat` : 'Tidak ada',
          `${weeklyScore}%`,
        ]
      } else {
        // Single day data within week
        const dailyScore = report.entry ? 100 : 0
        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          report.summaryDate,
          '1 hari',
          report.entry ? '1 hari' : '0 hari',
          report.lateEntry ? '1 hari' : '0 hari',
          report.excusedAbsence ? '1 hari' : '0 hari',
          report.sick ? '1 hari' : '0 hari',
          formatTimeToWITA(report.entryTime),
          formatTimeToWITA(report.lateEntryTime),
          `${dailyScore}%`,
        ]
      }
    })

    return [
      `=== LAPORAN SEKOLAH MINGGUAN - ${reportDate} ===`,
      `Waktu Zona: WITA (Asia/Makassar)`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Rata-rata Kehadiran Mingguan: ${Math.round(stats.entryPercentage)}%`,
      `Total Masuk (Minggu Ini): ${stats.entry}`,
      `Total Terlambat (Minggu Ini): ${stats.lateEntry}`,
      `Diekspor pada: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}`,
      '',
      headers.join(','),
      ...weeklyData.map(row => row.join(',')),
    ].join('\n')
  }

  // Range format CSV (30 days) with weekly breakdowns
  const generateRangeSchoolCSV = (
    reports: SchoolReport[],
    stats: SchoolStats,
    reportDate: string,
    className: string,
    formatTimeToWITA: (time: string | null | undefined) => string
  ): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Periode_30_Hari',
      'Total_Hari_Masuk',
      'Total_Hari_Terlambat',
      'Total_Hari_Ijin',
      'Total_Hari_Sakit',
      'Persentase_Kehadiran',
      'Rata_Rata_Waktu_Masuk',
      'Frekuensi_Terlambat',
      'Status_Kehadiran',
    ]

    const rangeData = reports.map((report, index) => {
      const isAggregated = (report as any).aggregatedCounts

      if (isAggregated) {
        const counts = (report as any).aggregatedCounts
        const totalDays = 30
        const attendanceRate = Math.round(
          (counts.entry / (totalDays - counts.excusedAbsence - counts.sick)) * 100
        )
        const lateFrequency =
          counts.lateEntry > 0 ? `${counts.lateEntry} dari ${counts.entry} hari` : 'Tidak pernah'

        let attendanceStatus = 'BAIK'
        if (attendanceRate < 80) attendanceStatus = 'PERLU_PERHATIAN'
        if (attendanceRate < 60) attendanceStatus = 'BURUK'

        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          '30 hari terakhir',
          `${counts.entry} hari`,
          `${counts.lateEntry} hari`,
          `${counts.excusedAbsence} hari`,
          `${counts.sick} hari`,
          `${attendanceRate}%`,
          counts.entry > 0 ? `${counts.entry} kali hadir` : 'Tidak ada data',
          lateFrequency,
          attendanceStatus,
        ]
      } else {
        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          report.summaryDate,
          report.entry ? '1 hari' : '0 hari',
          report.lateEntry ? '1 hari' : '0 hari',
          report.excusedAbsence ? '1 hari' : '0 hari',
          report.sick ? '1 hari' : '0 hari',
          report.entry ? '100%' : '0%',
          formatTimeToWITA(report.entryTime),
          report.lateEntry ? 'Ya' : 'Tidak',
          report.entry ? 'HADIR' : 'TIDAK_HADIR',
        ]
      }
    })

    return [
      `=== LAPORAN SEKOLAH 30 HARI - ${reportDate} ===`,
      `Waktu Zona: WITA (Asia/Makassar)`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Rata-rata Kehadiran: ${Math.round(stats.entryPercentage)}%`,
      `Total Kehadiran (30 Hari): ${stats.entry}`,
      `Total Keterlambatan (30 Hari): ${stats.lateEntry}`,
      `Diekspor pada: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}`,
      '',
      headers.join(','),
      ...rangeData.map(row => row.join(',')),
    ].join('\n')
  }

  // Monthly format CSV with consolidated monthly data
  const generateMonthlySchoolCSV = (
    reports: SchoolReport[],
    stats: SchoolStats,
    reportDate: string,
    className: string,
    formatTimeToWITA: (time: string | null | undefined) => string
  ): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Bulan_Tahun',
      'Hari_Sekolah_Bulan_Ini',
      'Total_Kehadiran',
      'Total_Keterlambatan',
      'Total_Ijin',
      'Total_Sakit',
      'Persentase_Kehadiran_Bulanan',
    ]

    const monthlyData = reports.map((report, index) => {
      const isAggregated = (report as any).aggregatedCounts

      if (isAggregated) {
        const counts = (report as any).aggregatedCounts
        const schoolDays = 22 // Average school days per month
        const availableDays = schoolDays - counts.excusedAbsence - counts.sick
        const monthlyRate =
          availableDays > 0 ? Math.round((counts.entry / availableDays) * 100) : 100

        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          reportDate,
          `${schoolDays} hari`,
          `${counts.entry} hari`,
          `${counts.lateEntry} hari`,
          `${counts.excusedAbsence} hari`,
          `${counts.sick} hari`,
          `${monthlyRate}%`,
        ]
      } else {
        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          reportDate,
          '1 hari',
          report.entry ? '1 hari' : '0 hari',
          report.lateEntry ? '1 hari' : '0 hari',
          report.excusedAbsence ? '1 hari' : '0 hari',
          report.sick ? '1 hari' : '0 hari',
          report.entry ? '100%' : '0%',
        ]
      }
    })

    return [
      `=== LAPORAN SEKOLAH BULANAN - ${reportDate} ===`,
      `Waktu Zona: WITA (Asia/Makassar)`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Rata-rata Kehadiran Bulanan: ${Math.round(stats.entryPercentage)}%`,
      `Total Hari Sekolah (Bulan Ini): ~22 hari`,
      `Total Kehadiran: ${stats.entry}`,
      `Tingkat Keterlambatan: ${Math.round(stats.lateEntryPercentage)}%`,
      `Diekspor pada: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}`,
      '',
      headers.join(','),
      ...monthlyData.map(row => row.join(',')),
    ].join('\n')
  }

  // Yearly format CSV with annual summary
  const generateYearlySchoolCSV = (
    reports: SchoolReport[],
    stats: SchoolStats,
    reportDate: string,
    className: string,
    formatTimeToWITA: (time: string | null | undefined) => string
  ): string => {
    const headers = [
      'No',
      'Kode_Siswa',
      'Nama_Lengkap',
      'Kelas',
      'Tahun_Ajaran',
      'Total_Hari_Sekolah',
      'Total_Kehadiran',
      'Total_Keterlambatan',
      'Total_Ijin',
      'Total_Sakit',
      'Persentase_Kehadiran_Tahunan',
    ]

    const yearlyData = reports.map((report, index) => {
      const isAggregated = (report as any).aggregatedCounts

      if (isAggregated) {
        const counts = (report as any).aggregatedCounts
        const schoolDays = 200 // Total school days in a year
        const availableDays = schoolDays - counts.excusedAbsence - counts.sick
        const yearlyRate =
          availableDays > 0 ? Math.round((counts.entry / availableDays) * 100) : 100

        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          reportDate,
          `${schoolDays} hari`,
          `${counts.entry} hari`,
          `${counts.lateEntry} hari`,
          `${counts.excusedAbsence} hari`,
          `${counts.sick} hari`,
          `${yearlyRate}%`,
        ]
      } else {
        return [
          (index + 1).toString(),
          report.uniqueCode,
          report.name,
          report.className,
          reportDate,
          '1 hari',
          report.entry ? '1 hari' : '0 hari',
          report.lateEntry ? '1 hari' : '0 hari',
          report.excusedAbsence ? '1 hari' : '0 hari',
          report.sick ? '1 hari' : '0 hari',
          report.entry ? '100%' : '0%',
        ]
      }
    })

    return [
      `=== LAPORAN SEKOLAH TAHUNAN - ${reportDate} ===`,
      `Waktu Zona: WITA (Asia/Makassar)`,
      `Filter Kelas: ${className}`,
      `Total Siswa: ${stats.total}`,
      `Rata-rata Kehadiran Tahunan: ${Math.round(stats.entryPercentage)}%`,
      `Total Hari Sekolah (Tahun Ini): ~200 hari`,
      `Total Kehadiran: ${stats.entry}`,
      `Tingkat Keterlambatan: ${Math.round(stats.lateEntryPercentage)}%`,
      `Diekspor pada: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}`,
      '',
      headers.join(','),
      ...yearlyData.map(row => row.join(',')),
    ].join('\n')
  }

  // Export school reports to CSV
  const handleExport = async () => {
    try {
      setIsExporting(true)

      // Generate school-specific CSV content
      const csvContent = generateSchoolCSV(reports, stats)

      // Create download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `laporan-sekolah-${format(new Date(), 'yyyy-MM-dd')}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: 'Laporan sekolah berhasil diekspor',
        description: 'File CSV telah diunduh ke perangkat Anda',
      })
    } catch (error) {
      console.error('Error exporting school reports:', error)
      toast({
        title: 'Gagal mengekspor laporan',
        description: 'Terjadi kesalahan saat mengekspor data',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Filter reports based on search
  const filteredReports = reports.filter(report => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return (
      report.name.toLowerCase().includes(query) ||
      report.uniqueCode.toLowerCase().includes(query) ||
      report.className.toLowerCase().includes(query)
    )
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedReports = filteredReports.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [classFilter, date, searchQuery, selectedMonth, selectedYear])

  // Load data on component mount and when filters change
  useEffect(() => {
    if (
      admin &&
      (admin.role === 'super_admin' || admin.role === 'teacher' || admin.role === 'receptionist')
    ) {
      fetchReports()
    }
  }, [admin, date, classFilter, selectedMonth, selectedYear])

  // Modern Loading Component with Skeleton Screens
  const LoadingSkeleton = () => (
    <div className="space-y-6 duration-500 animate-in fade-in-50">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-4 w-96 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-10 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
      </div>

      {/* Report Type Selector Skeleton */}
      <div className="mb-6 flex flex-wrap gap-2">
        {[...Array(3)].map((_, i) => (
          <Skeleton
            key={i}
            className="h-10 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
          />
        ))}
      </div>

      {/* Filter Controls Skeleton */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 flex-1 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
                <Skeleton className="h-12 w-12 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Chart Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              <Skeleton className="h-4 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Chart Area */}
            <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />

            {/* Chart Legend */}
            <div className="flex justify-center gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-4 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            <Skeleton className="h-4 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 border-b pb-2">
              {[...Array(8)].map((_, i) => (
                <Skeleton
                  key={i}
                  className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                />
              ))}
            </div>

            {/* Table Rows */}
            {[...Array(8)].map((_, i) => (
              <div key={i} className="grid grid-cols-8 gap-4 py-2">
                {[...Array(8)].map((_, j) => (
                  <Skeleton
                    key={j}
                    className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                  />
                ))}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Loading Indicator */}
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-3 text-gray-500">
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          <span className="text-sm font-medium">Memuat data laporan sekolah...</span>
        </div>
      </div>
    </div>
  )

  // Show loading state
  if (sessionLoading || !admin) {
    return <LoadingSkeleton />
  }

  // Show loading skeleton when fetching data
  if (isLoading) {
    return <LoadingSkeleton />
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'teacher' && admin.role !== 'receptionist') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan sekolah.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Sekolah</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran sekolah siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchReports} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Ekspor CSV
          </Button>
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="mb-6 flex flex-wrap gap-2">
        <Button
          variant={!['monthly', 'yearly'].includes(date) ? 'default' : 'outline'}
          onClick={() => setDate('today')}
          className="whitespace-nowrap"
        >
          📊 Laporan Harian
        </Button>
        <Button
          variant={date === 'monthly' ? 'default' : 'outline'}
          onClick={() => setDate('monthly')}
          className="whitespace-nowrap"
        >
          📅 Laporan Bulanan
        </Button>
        <Button
          variant={date === 'yearly' ? 'default' : 'outline'}
          onClick={() => setDate('yearly')}
          className="whitespace-nowrap"
        >
          📈 Laporan Tahunan
        </Button>
      </div>

      {/* Daily Report Filters - Only show for daily reports */}
      {!['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <Select value={date} onValueChange={setDate}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="yesterday">Kemarin</SelectItem>
              <SelectItem value="week">Minggu Ini (7 Hari)</SelectItem>
              <SelectItem value="30days">30 Hari Terakhir</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* Monthly/Yearly Report Filters */}
      {['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>

          {date === 'monthly' && (
            <Select
              value={selectedMonth.toString()}
              onValueChange={value => setSelectedMonth(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Bulan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Januari</SelectItem>
                <SelectItem value="2">Februari</SelectItem>
                <SelectItem value="3">Maret</SelectItem>
                <SelectItem value="4">April</SelectItem>
                <SelectItem value="5">Mei</SelectItem>
                <SelectItem value="6">Juni</SelectItem>
                <SelectItem value="7">Juli</SelectItem>
                <SelectItem value="8">Agustus</SelectItem>
                <SelectItem value="9">September</SelectItem>
                <SelectItem value="10">Oktober</SelectItem>
                <SelectItem value="11">November</SelectItem>
                <SelectItem value="12">Desember</SelectItem>
              </SelectContent>
            </Select>
          )}

          {date === 'yearly' && (
            <Select
              value={selectedYear.toString()}
              onValueChange={value => setSelectedYear(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Tahun" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2025">2025</SelectItem>
                <SelectItem value="2026">2026</SelectItem>
                <SelectItem value="2027">2027</SelectItem>
                <SelectItem value="2028">2028</SelectItem>
                <SelectItem value="2029">2029</SelectItem>
                <SelectItem value="2030">2030</SelectItem>
              </SelectContent>
            </Select>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* KPI Cards - Split into 6 separate cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                    <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                    <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-green-600">{stats.entry}</div>
                    <div className="text-sm font-medium text-gray-600">Masuk</div>
                    <div className="mt-1 text-xs text-gray-500">
                      {stats.entryPercentage}% kehadiran
                    </div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-amber-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-amber-600">{stats.lateEntry}</div>
                    <div className="text-sm font-medium text-gray-600">Terlambat</div>
                    <div className="mt-1 text-xs text-gray-500">
                      {stats.lateEntryPercentage}% siswa
                    </div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-orange-600">{stats.excusedAbsence}</div>
                    <div className="text-sm font-medium text-gray-600">Ijin</div>
                    <div className="mt-1 text-xs text-gray-500">
                      {stats.excusedAbsencePercentage}% siswa
                    </div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-red-600">{stats.sick}</div>
                    <div className="text-sm font-medium text-gray-600">Sakit</div>
                    <div className="mt-1 text-xs text-gray-500">{stats.sickPercentage}% siswa</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-purple-600">
                      {stats.temporaryLeave + stats.returnFromLeave}
                    </div>
                    <div className="text-sm font-medium text-gray-600">Ijin Keluar</div>
                    <div className="mt-1 text-xs text-gray-500">
                      {Math.round(stats.temporaryLeavePercentage + stats.returnFromLeavePercentage)}
                      % siswa
                    </div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* School Attendance Trend Chart with 4 separate lines */}
      <div className="hidden md:block">
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Tren Kehadiran Sekolah
                </CardTitle>
                <p className="text-sm text-gray-500">
                  Grafik perbandingan kehadiran, keterlambatan, ijin, dan sakit
                </p>
              </div>
              <div className="text-gray-400">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <circle cx="12" cy="6" r="1" />
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="12" cy="18" r="1" />
                </svg>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            ) : trendData.length === 0 ? (
              <div className="flex h-80 items-center justify-center text-gray-500">
                <div className="text-center">
                  <p className="text-lg font-medium">Tidak ada data trend</p>
                  <p className="text-sm">Data trend akan muncul setelah ada data kehadiran</p>
                </div>
              </div>
            ) : (
              <>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={trendData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    >
                      <defs>
                        <linearGradient id="entryGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#10b981" stopOpacity={0.1} />
                          <stop offset="100%" stopColor="#10b981" stopOpacity={0} />
                        </linearGradient>
                        <linearGradient id="lateGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.1} />
                          <stop offset="100%" stopColor="#f59e0b" stopOpacity={0} />
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                      <XAxis
                        dataKey="date"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#94a3b8' }}
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 12, fill: '#94a3b8' }}
                      />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="rounded-lg bg-gray-900 px-3 py-2 text-white shadow-lg">
                                <p className="font-medium">{label}</p>
                                {payload.map((entry, index) => (
                                  <p key={index} className="text-sm">
                                    <span
                                      className="mr-2 inline-block h-2 w-2 rounded-full"
                                      style={{ backgroundColor: entry.color }}
                                    ></span>
                                    {entry.name}: {entry.value}
                                  </p>
                                ))}
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                      {/* Entry/Kehadiran Line */}
                      <Line
                        type="monotone"
                        dataKey="entry"
                        stroke="#10b981"
                        strokeWidth={3}
                        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, fill: '#10b981', strokeWidth: 2, stroke: '#fff' }}
                        name="Kehadiran"
                      />
                      {/* Late Entry/Terlambat Line */}
                      <Line
                        type="monotone"
                        dataKey="lateEntry"
                        stroke="#f59e0b"
                        strokeWidth={2}
                        dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
                        activeDot={{ r: 5, fill: '#f59e0b', strokeWidth: 2, stroke: '#fff' }}
                        name="Terlambat"
                      />
                      {/* Excused Absence/Ijin Line */}
                      <Line
                        type="monotone"
                        dataKey="excusedAbsence"
                        stroke="#f97316"
                        strokeWidth={2}
                        dot={{ fill: '#f97316', strokeWidth: 2, r: 3 }}
                        activeDot={{ r: 5, fill: '#f97316', strokeWidth: 2, stroke: '#fff' }}
                        name="Ijin"
                      />
                      {/* Sick Line */}
                      <Line
                        type="monotone"
                        dataKey="sick"
                        stroke="#ef4444"
                        strokeWidth={2}
                        dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
                        activeDot={{ r: 5, fill: '#ef4444', strokeWidth: 2, stroke: '#fff' }}
                        name="Sakit"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                {/* Chart Legend */}
                <div className="mt-4 flex justify-center gap-6">
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                    <span className="text-sm text-gray-600">Kehadiran</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-amber-500"></div>
                    <span className="text-sm text-gray-600">Terlambat</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-orange-500"></div>
                    <span className="text-sm text-gray-600">Ijin</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full bg-red-500"></div>
                    <span className="text-sm text-gray-600">Sakit</span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Student Data Matrix - Moved to Bottom */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Sekolah ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead>Kode Siswa</TableHead>
                  <TableHead>Nama</TableHead>
                  <TableHead>Kelas</TableHead>
                  <TableHead>Masuk</TableHead>
                  <TableHead>Terlambat</TableHead>
                  <TableHead>Ijin</TableHead>
                  <TableHead>Sakit</TableHead>
                  <TableHead>Ijin Keluar</TableHead>
                  <TableHead>Kembali</TableHead>
                  {/* Hide score column for daily filters (today, yesterday) */}
                  {!['today', 'yesterday'].includes(date) && <TableHead>Skor</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      {/* Hide score column for daily filters (today, yesterday) */}
                      {!['today', 'yesterday'].includes(date) && (
                        <TableCell>
                          <Skeleton className="h-4 w-16" />
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                ) : paginatedReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={11} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedReports.map((report, index) => {
                    const globalIndex = startIndex + index + 1
                    const isAggregatedData = ['week', '30days', 'monthly', 'yearly'].includes(date)

                    // Calculate attendance metrics based on data type
                    let attendanceCount,
                      attendanceScore,
                      entryDisplay,
                      lateEntryDisplay,
                      excusedAbsenceDisplay,
                      sickDisplay,
                      temporaryLeaveDisplay,
                      returnFromLeaveDisplay

                    if (isAggregatedData && (report as any).aggregatedCounts) {
                      const counts = (report as any).aggregatedCounts

                      // PROPER SCHOOL ATTENDANCE SCORING FORMULA
                      // Attendance Score = Days Present / Total School Days × 100%
                      // Someone who always does entry + pulang = 100%
                      // NOTE: Temporary Leave and Return from Leave do NOT affect scoring
                      let totalSchoolDays = 1
                      if (date === 'week') totalSchoolDays = 7
                      else if (date === '30days') totalSchoolDays = 30
                      else if (date === 'monthly') totalSchoolDays = 30
                      else if (date === 'yearly') totalSchoolDays = 200

                      attendanceCount = counts.entry

                      // If student has ijin/sick, calculate based on available days
                      // NOTE: temporaryLeave and returnFromLeave are NOT counted as excused days
                      const excusedDays = counts.excusedAbsence + counts.sick
                      if (excusedDays > 0) {
                        const availableDays = totalSchoolDays - excusedDays
                        attendanceScore =
                          availableDays > 0 ? Math.round((counts.entry / availableDays) * 100) : 100
                      } else {
                        attendanceScore = Math.round((counts.entry / totalSchoolDays) * 100)
                      }

                      // Show total counts for aggregated data
                      entryDisplay = `${counts.entry} total`
                      lateEntryDisplay = counts.lateEntry > 0 ? `${counts.lateEntry} total` : '-'
                      excusedAbsenceDisplay =
                        counts.excusedAbsence > 0 ? `${counts.excusedAbsence} hari` : '-'
                      sickDisplay = counts.sick > 0 ? `${counts.sick} hari` : '-'
                      temporaryLeaveDisplay =
                        counts.temporaryLeave > 0 ? `${counts.temporaryLeave} total` : '-'
                      returnFromLeaveDisplay =
                        counts.returnFromLeave > 0 ? `${counts.returnFromLeave} total` : '-'
                    } else {
                      // Show checkmarks for today/yesterday
                      // PROPER DAILY SCHOOL ATTENDANCE SCORING
                      // Today: 0% (absent), 100% (present)
                      // NOTE: Temporary Leave and Return from Leave do NOT affect scoring
                      attendanceCount = report.entry ? 1 : 0

                      if (report.excusedAbsence || report.sick) {
                        attendanceScore = 100 // Ijin/Sakit = excused, full score
                      } else {
                        attendanceScore = attendanceCount * 100 // 0% or 100%
                      }

                      entryDisplay = report.entry ? '✓' : '✗'
                      lateEntryDisplay = report.lateEntry ? 'Terlambat' : '-'
                      excusedAbsenceDisplay = report.excusedAbsence ? 'Ijin' : '-'
                      sickDisplay = report.sick ? 'Sakit' : '-'
                      temporaryLeaveDisplay = report.temporaryLeave ? 'Ya' : '-'
                      returnFromLeaveDisplay = report.returnFromLeave ? 'Ya' : '-'
                    }

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{globalIndex}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <Badge variant={report.entry ? 'default' : 'secondary'}>
                            {entryDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.lateEntry ? 'destructive' : 'secondary'}>
                            {lateEntryDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.excusedAbsence ? 'outline' : 'secondary'}>
                            {excusedAbsenceDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.sick ? 'outline' : 'secondary'}>
                            {sickDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.temporaryLeave ? 'outline' : 'secondary'}>
                            {temporaryLeaveDisplay}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.returnFromLeave ? 'outline' : 'secondary'}>
                            {returnFromLeaveDisplay}
                          </Badge>
                        </TableCell>
                        {/* Hide score column for daily filters (today, yesterday) */}
                        {!['today', 'yesterday'].includes(date) && (
                          <TableCell>
                            <Badge
                              variant={
                                attendanceScore >= 80
                                  ? 'default'
                                  : attendanceScore >= 50
                                    ? 'secondary'
                                    : 'destructive'
                              }
                            >
                              {attendanceScore}%
                            </Badge>
                          </TableCell>
                        )}
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredReports.length)} dari{' '}
                {filteredReports.length} siswa
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={
                        currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      className={
                        currentPage === totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
