import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'

// Initialize dependencies
const cache = getRedisCache()
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

/**
 * Creates a date object for a specific day with time set to 00:00:00
 * This function ensures consistent date creation across server and client
 *
 * @param targetDate - The date to use (defaults to current date)
 * @param dayOffset - Number of days to offset (negative for past, positive for future)
 * @returns Date object set to the start of the day in the database's time
 */
function createDateForDay(targetDate = new Date(), dayOffset = 0): Date {
  // Create a clean date with just year, month, and day components (no time)
  // This helps avoid timezone-related inconsistencies
  const year = targetDate.getFullYear()
  const month = targetDate.getMonth()
  const day = targetDate.getDate()

  // Create a new date with just the date components
  const date = new Date(year, month, day + dayOffset)

  // Ensure time is set to the start of the day (00:00:00.000)
  date.setHours(0, 0, 0, 0)

  return date
}

/**
 * Converts database timestamp value to consistent date format
 * This ensures we convert dates to the same consistent format for filtering
 *
 * @param isoString - ISO date string
 * @returns Date object
 */
function parseDatabaseDate(isoString: string): Date {
  // If the string doesn't include timezone info, treat it as database time
  if (!isoString.endsWith('Z') && !isoString.includes('+')) {
    // Append Z to indicate this is UTC time
    return new Date(`${isoString}Z`)
  }
  return new Date(isoString)
}

/**
 * GET /api/absence/reports
 * Get attendance summary for reporting
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Create a unique log identifier for this request to track it in logs
    const requestId = Math.random().toString(36).substring(2, 8)

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const dateParam = searchParams.get('date')
    const className = searchParams.get('class')
    const reportType = searchParams.get('reportType') || 'all' // 'prayer' | 'school' | 'all'
    const forceFresh = searchParams.get('force_fresh') === 'true'
    const debug = searchParams.get('debug') === 'true'
    const monthParam = searchParams.get('month')
    const yearParam = searchParams.get('year')

    // Parse date parameter
    let date: Date | undefined
    let isWeekFilter = false
    let isRangeFilter = false
    let startDate: Date | undefined
    let endDate: Date | undefined

    if (dateParam) {
      if (dateParam === 'today') {
        // Use current date set to start of day
        date = createDateForDay()
      } else if (dateParam === 'yesterday') {
        // Create date for yesterday using the offset approach
        date = createDateForDay(new Date(), -1)
      } else if (dateParam === 'week') {
        // 7 days range filter (including today)
        isRangeFilter = true
        endDate = createDateForDay()
        startDate = new Date(endDate)
        startDate.setDate(startDate.getDate() - 6) // 7 days including today
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] Week range: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else if (dateParam === '30days') {
        // 30 days range filter
        isRangeFilter = true
        endDate = createDateForDay()
        startDate = new Date(endDate)
        startDate.setDate(startDate.getDate() - 29) // 30 days including today
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] 30 days range: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else if (dateParam === 'current-month') {
        // Current month filter
        isRangeFilter = true
        const now = createDateForDay()
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        startDate.setHours(0, 0, 0, 0)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] Current month range: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else if (dateParam === 'last-3months') {
        // Last 3 months filter
        isRangeFilter = true
        endDate = createDateForDay()
        startDate = new Date(endDate)
        startDate.setMonth(startDate.getMonth() - 3)
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] Last 3 months range: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else if (dateParam === 'monthly') {
        // Monthly summary - return aggregated monthly data
        isRangeFilter = true
        const month = monthParam ? parseInt(monthParam) : new Date().getMonth() + 1
        const year = yearParam ? parseInt(yearParam) : new Date().getFullYear()

        // Get specific month data
        startDate = new Date(year, month - 1, 1) // First day of month
        endDate = new Date(year, month, 0) // Last day of month
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] Monthly summary for ${month}/${year}: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else if (dateParam === 'yearly') {
        // Yearly summary - return aggregated yearly data
        isRangeFilter = true
        const year = yearParam ? parseInt(yearParam) : new Date().getFullYear()

        // Get specific year data
        startDate = new Date(year, 0, 1) // January 1st
        endDate = new Date(year, 11, 31) // December 31st
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        console.log(
          `[${requestId}] Yearly summary for ${year}: ${startDate.toISOString()} to ${endDate.toISOString()}`
        )
      } else {
        // Parse the date string (should be in YYYY-MM-DD format)
        try {
          const [year, month, day] = dateParam.split('-').map(Number)

          // Create a date in the server's timezone
          date = new Date(year, month - 1, day)
          date.setHours(0, 0, 0, 0)

          if (isNaN(date.getTime())) {
            console.error(`[${requestId}] Invalid date format received: ${dateParam}`)
            return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
          }
        } catch (error) {
          console.error(`[${requestId}] Error parsing date ${dateParam}:`, error)
          return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
        }
      }
    } else {
      // Default to today if no date parameter is provided
      date = createDateForDay()
    }

    // If forceFresh is true, refresh the materialized view and clear cache
    if (forceFresh) {
      try {
        // Refresh the materialized view
        await absenceUseCases.refreshAttendanceSummary()
      } catch (error) {
        console.error(`[${requestId}] Error refreshing materialized view:`, error)
        // Continue execution even if this fails
      }
    }

    // Get attendance summary with report type filtering
    let summary
    if (isRangeFilter && startDate && endDate) {
      // Use range-based aggregated summary for longer periods
      summary = await absenceUseCases.getAggregatedAttendanceSummary(
        startDate,
        endDate,
        className || undefined,
        reportType as 'prayer' | 'school' | 'all',
        forceFresh
      )
    } else {
      // Use regular summary for daily/weekly reports
      summary = await absenceUseCases.getAttendanceSummaryByType(
        date,
        className || undefined,
        isWeekFilter,
        reportType as 'prayer' | 'school' | 'all',
        forceFresh
      )
    }

    if (debug) {
      // For debugging, get the server and database time information
      const serverTime = new Date()

      // Check the database's concept of "now" by logging

      console.log(
        `[${requestId}] Server timezone offset: ${-serverTime.getTimezoneOffset() / 60} hours`
      )

      // Return diagnostic information along with the results
      return NextResponse.json(
        {
          debug: {
            serverTime: serverTime.toISOString(),
            serverTimezoneOffset: -serverTime.getTimezoneOffset() / 60,
            requestedDate: date ? date.toISOString() : null,
            dateParam,
            isWeekFilter,
            recordCount: summary.length,
          },
          data: summary,
        },
        {
          headers: {
            'Cache-Control': 'no-store, max-age=0',
            'X-Request-Time': new Date().toISOString(),
          },
        }
      )
    }

    // Return the summary with cache control headers
    return NextResponse.json(summary, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'X-Request-Time': new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error('Error getting attendance summary:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
