import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import QRCode from 'qrcode'
import JSZip from 'jszip'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

/**
 * POST /api/students/bulk-qr-download
 * Generate and download QR codes for multiple students as a ZIP file
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const body = await req.json()
    const { studentIds, classId, downloadType, gender } = body

    // Validate request parameters
    if (!downloadType || !['all', 'selected', 'class'].includes(downloadType)) {
      return NextResponse.json(
        { error: 'Invalid download type. Must be "all", "selected", or "class"' },
        { status: 400 }
      )
    }

    if (
      downloadType === 'selected' &&
      (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0)
    ) {
      return NextResponse.json(
        { error: 'Student IDs are required for selected download type' },
        { status: 400 }
      )
    }

    if (downloadType === 'class' && !classId) {
      return NextResponse.json(
        { error: 'Class ID is required for class download type' },
        { status: 400 }
      )
    }

    // Fetch students based on download type
    let students: any[] = []

    try {
      if (downloadType === 'all') {
        // Get all students
        const allUsers = await userUseCases.getAllUsers()
        students = allUsers.filter(user => user.role === 'student')
        if (gender && gender !== 'all') {
          students = students.filter(user => user.gender === gender)
        }
      } else if (downloadType === 'selected') {
        // Get selected students
        const selectedStudents = await Promise.all(
          studentIds.map(async (id: number) => {
            try {
              return await userUseCases.getUserById(id)
            } catch (error) {
              console.warn(`Failed to fetch student with ID ${id}:`, error)
              return null
            }
          })
        )
        students = selectedStudents.filter(student => student && student.role === 'student')
        if (gender && gender !== 'all') {
          students = students.filter(student => student.gender === gender)
        }
      } else if (downloadType === 'class') {
        // Get students by class
        const allUsers = await userUseCases.getAllUsers()
        students = allUsers.filter(
          user => user.role === 'student' && user.classId === parseInt(classId)
        )
        if (gender && gender !== 'all') {
          students = students.filter(user => user.gender === gender)
        }
      }

      if (students.length === 0) {
        return NextResponse.json(
          { error: 'No students found for the specified criteria' },
          { status: 404 }
        )
      }

      // Create ZIP file
      const zip = new JSZip()
      const qrFolder = zip.folder('qr-codes')

      if (!qrFolder) {
        throw new Error('Failed to create QR codes folder in ZIP')
      }

      // Generate QR codes for each student
      const qrPromises = students.map(async student => {
        try {
          if (!student.uniqueCode) {
            console.warn(`Student ${student.name} (ID: ${student.id}) has no unique code`)
            return null
          }

          // Generate QR code as PNG buffer
          const qrBuffer = await QRCode.toBuffer(student.uniqueCode, {
            type: 'png',
            width: 300,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF',
            },
          })

          // Create safe filename
          const safeName = student.name
            .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .toLowerCase()

          const className = student.className
            ? student.className.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '-')
            : 'no-class'
          const nis = student.nis || 'no-nis'
          const genderLabel = (gender?: string) => {
            if (gender === 'male') return 'Laki-laki'
            if (gender === 'female') return 'Perempuan'
            return 'no-gender'
          }
          const filename = `${safeName}_${nis}_${student.uniqueCode.substring(0, 8)}_${className}_${genderLabel(student.gender)}.png`

          return {
            filename,
            buffer: qrBuffer,
            student: student.name,
          }
        } catch (error) {
          console.error(`Failed to generate QR code for student ${student.name}:`, error)
          return null
        }
      })

      const qrResults = await Promise.all(qrPromises)
      const successfulQRs = qrResults.filter(result => result !== null)

      if (successfulQRs.length === 0) {
        return NextResponse.json({ error: 'Failed to generate any QR codes' }, { status: 500 })
      }

      // Add QR codes to ZIP
      successfulQRs.forEach(qr => {
        if (qr) {
          qrFolder.file(qr.filename, qr.buffer)
        }
      })

      // Add a README file with instructions
      const readmeContent = `QR Code Absensi - SMK Negeri 3 Banjarmasin

Generated: ${new Date().toLocaleString('id-ID', { timeZone: 'Asia/Makassar' })}
Total QR Codes: ${successfulQRs.length}
Download Type: ${downloadType}

Instructions:
1. Each QR code file is named with the student's name and unique code
2. Print these QR codes and distribute to students
3. Students can use these QR codes for attendance scanning
4. Each QR code is unique to the student and should not be shared

For support, contact the school administration.
`

      zip.file('README.txt', readmeContent)

      // Generate ZIP file
      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' })

      // Create filename for download
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      let zipFilename = `qr_codes_${downloadType}_${timestamp}.zip`

      if (downloadType === 'class') {
        const classInfo = students[0]?.className || `class_${classId}`
        const safeClassName = classInfo.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')
        zipFilename = `qr_codes_${safeClassName}_${timestamp}.zip`
      }

      // Return ZIP file as response
      return new NextResponse(zipBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${zipFilename}"`,
          'Content-Length': zipBuffer.length.toString(),
        },
      })
    } catch (error) {
      console.error('Error fetching students or generating QR codes:', error)
      return NextResponse.json(
        { error: 'Failed to fetch students or generate QR codes' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in bulk QR download:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
