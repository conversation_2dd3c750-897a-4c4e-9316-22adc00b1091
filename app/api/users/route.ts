import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache) // Assuming StudentRepository takes cache
const adminRepo = new AdminRepository(cache) // Assuming AdminRepository takes cache
const classRepo = new ClassRepository() // Assuming ClassRepository doesn't need cache directly here
const absenceRepo = new AbsenceRepository() // Initialize the AbsenceRepository without parameters
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Base schemas
const baseUserSchema = z.object({
  id: z.string(), // Assuming id is always a string from the use case
  name: z.string(),
})

const adminSchema = baseUserSchema.extend({
  username: z.string(),
  role: z.enum(['admin', 'super_admin']),
})

const studentSchema = baseUserSchema.extend({
  uniqueCode: z.string(),
  googleEmail: z.string().email().optional(), // Optional as per your previous code
  nis: z.string().optional(),
  className: z.string().optional(),
  role: z.literal('student'),
})

// Type for users returned by use case - this is an assumption based on errors
type DomainUser = z.infer<typeof adminSchema> | z.infer<typeof studentSchema>

// Validation schema for creating an admin (payload for API)
const createAdminPayloadSchema = z.object({
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter'),
  name: z.string().min(1, 'Nama wajib diisi').max(100, 'Nama maksimal 100 karakter'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
})

// Validation schema for creating a student (payload for API)
const createStudentPayloadSchema = z.object({
  name: z.string().min(1, 'Nama wajib diisi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  classId: z.coerce.number().int().positive('ID Kelas harus angka positif').nullable().optional(),
  gender: z.enum(['male', 'female']).optional(),
})

// Combined schema for POST request body, discriminated by role
const userCreateApiSchema = z.discriminatedUnion('role', [
  z.object({
    role: z.enum(['Admin', 'admin', 'super_admin', 'Super_admin']),
    name: z.string().min(1, 'Nama wajib diisi').max(100, 'Nama maksimal 100 karakter'),
    username: z
      .string()
      .min(3, 'Username minimal 3 karakter')
      .max(50, 'Username maksimal 50 karakter'),
    password: z.string().min(6, 'Password minimal 6 karakter'),
  }),
  z.object({
    role: z.enum(['Student', 'student']),
    name: z.string().min(1, 'Nama wajib diisi'),
    username: z
      .string()
      .min(3, 'Username minimal 3 karakter')
      .max(50, 'Username maksimal 50 karakter'),
    password: z.string().min(6, 'Password minimal 6 karakter'),
    classId: z.coerce.number().int().positive('ID Kelas harus angka positif').nullable().optional(),
    gender: z.enum(['male', 'female']).optional(),
  }),
])

/**
 * GET /api/users
 * Get all users (students and admins)
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    console.info('Fetching all users')
    const users = await userUseCases.getAllUsers() // This should return (Student | Admin)[]

    // Map users to a safe response format using the actual role from the database
    const safeUsers = users
      .map((user: any) => {
        // Check the role field to correctly determine if it's an admin or student
        if (user.role === 'admin' || user.role === 'super_admin') {
          return {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role, // Preserve the actual role (admin or super_admin)
          }
        } else if (user.role === 'student') {
          return {
            id: user.id,
            username: user.username,
            uniqueCode: user.uniqueCode,
            googleEmail: user.googleEmail,
            name: user.name,
            nis: user.nis,
            className: user.className,
            classId: user.classId,
            gender: user.gender,
            role: 'student',
          }
        }
        console.warn('User object with unknown role encountered:', user)
        return null
      })
      .filter(Boolean)

    return NextResponse.json(safeUsers || [])
  } catch (error) {
    console.error('Failed to fetch users:', error instanceof Error ? error.message : error)
    return NextResponse.json(
      {
        message: 'Gagal mengambil data user',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/users
 * Create a new admin or student
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const body = await req.json()
    const validation = userCreateApiSchema.safeParse(body)

    if (!validation.success) {
      console.error('User creation validation failed:', validation.error.format())
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validation.data
    let newUser

    if (
      validatedData.role === 'Admin' ||
      validatedData.role === 'admin' ||
      validatedData.role === 'super_admin' ||
      validatedData.role === 'Super_admin'
    ) {
      console.info('Attempting to create Admin:', {
        username: validatedData.username,
        name: validatedData.name,
        role: validatedData.role,
      })
      newUser = await userUseCases.createAdmin({
        username: validatedData.username,
        name: validatedData.name,
        password: validatedData.password, // Password from validatedData
        role: validatedData.role.toLowerCase() as 'admin' | 'super_admin', // Normalize the role
      })
      console.info('Admin created successfully:', { id: (newUser as any).id })
    } else if (validatedData.role === 'Student' || validatedData.role === 'student') {
      console.info('Attempting to create Student:', {
        name: validatedData.name,
        username: validatedData.username,
        classId: validatedData.classId,
      })
      newUser = await userUseCases.createStudent({
        name: validatedData.name,
        username: validatedData.username,
        password: validatedData.password,
        classId: validatedData.classId === null ? undefined : validatedData.classId,
        gender: validatedData.gender,
      })
      console.info('Student created successfully:', { id: (newUser as any).id })
    } else {
      console.error(
        'Invalid role specified for user creation - this should not happen with discriminated union'
      )
      return NextResponse.json({ message: 'Role tidak valid' }, { status: 400 })
    }

    await cache.del('users:all')
    console.info('User cache invalidated: users:all')

    return NextResponse.json(newUser, { status: 201 })
  } catch (error) {
    console.error('Failed to create user:', error instanceof Error ? error.stack : error)
    const errorMessage = error instanceof Error ? error.message : 'Gagal membuat user baru'

    if (
      errorMessage.toLowerCase().includes('unique constraint') ||
      errorMessage.toLowerCase().includes('violates unique constraint users_username_key') ||
      errorMessage.toLowerCase().includes('violates unique constraint users_unique_code_key')
    ) {
      return NextResponse.json(
        { message: 'Username atau Kode Unik sudah ada.', error: errorMessage },
        { status: 409 }
      )
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Input tidak valid', error: error.format() },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: 'Gagal membuat user baru', error: errorMessage },
      { status: 500 }
    )
  }
}
