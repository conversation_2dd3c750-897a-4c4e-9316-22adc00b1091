import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { parse } from 'csv-parse/sync'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Schema for a single student row in CSV
const csvStudentSchema = z.object({
  name: z.string().min(1, 'Nama wajib diisi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  className: z.string().min(1, 'Nama kelas wajib diisi'),
  nis: z.string().optional(),
  googleEmail: z
    .string()
    .refine(val => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Format email tidak valid',
    })
    .optional()
    .transform(val => (val === '' ? undefined : val)),
  whatsapp: z.string().optional(),
  gender: z.string().min(1, 'Gender wajib diisi'),
})

type CSVStudent = z.infer<typeof csvStudentSchema>

// Helper to normalize gender
function normalizeGender(g: any): 'male' | 'female' | undefined {
  if (!g) return undefined
  if (typeof g === 'string') {
    const s = g.trim().toLowerCase()
    if (s === 'laki-laki' || s === 'male' || s === 'l') return 'male'
    if (s === 'perempuan' || s === 'female' || s === 'p') return 'female'
  }
  return undefined
}

/**
 * POST /api/users/bulk-upload
 * Upload a CSV file with multiple students
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    // Check if the request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { message: 'Content-Type harus multipart/form-data' },
        { status: 400 }
      )
    }

    // Parse the form data
    const formData = await req.formData()
    const file = formData.get('file') as File | null

    if (!file) {
      return NextResponse.json({ message: 'File CSV tidak ditemukan' }, { status: 400 })
    }

    // Verify file extension
    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({ message: 'File harus berformat CSV' }, { status: 400 })
    }

    // Read the file content
    const fileContent = await file.text()

    // Parse CSV content
    let records: any[]
    try {
      records = parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      })
    } catch (error) {
      console.error('Failed to parse CSV:', error)
      return NextResponse.json(
        { message: 'Format CSV tidak valid', error: (error as Error).message },
        { status: 400 }
      )
    }

    if (records.length === 0) {
      return NextResponse.json({ message: 'CSV tidak berisi data' }, { status: 400 })
    }

    // Validate and process the records in batches
    const batchSize = 100 // Process 100 records at a time
    const results = {
      total: records.length,
      success: 0,
      failed: 0,
      errors: [] as { row: number; message: string; data: any }[],
    }

    // Cache class IDs to reduce database queries
    const classIdCache = new Map<string, number>()

    // Process in batches
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      const batchPromises = batch.map(async (record, index) => {
        const rowIndex = i + index + 1 // CSV row number (1-indexed)

        try {
          // Validate CSV row data
          const validationResult = csvStudentSchema.safeParse(record)

          if (!validationResult.success) {
            const errorMessages = validationResult.error.format()
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: 'Validasi gagal: ' + JSON.stringify(errorMessages),
              data: record,
            })
            return null
          }

          const validData = validationResult.data

          // Find or create class
          let classId: number
          if (classIdCache.has(validData.className)) {
            classId = classIdCache.get(validData.className)!
          } else {
            const classObj = await userUseCases.findOrCreateClass(validData.className)
            classId = classObj.id
            classIdCache.set(validData.className, classId)
          }

          // Create student
          const normalizedGender = normalizeGender(validData.gender)
          if (!normalizedGender) {
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: 'Gender wajib diisi dengan "male" (Laki-laki) atau "female" (Perempuan)',
              data: record,
            })
            return null
          }
          await userUseCases.createStudent({
            name: validData.name,
            username: validData.username,
            password: validData.password,
            classId: classId,
            nis: validData.nis,
            googleEmail: validData.googleEmail,
            whatsapp: validData.whatsapp,
            gender: normalizedGender,
          })

          results.success++
          return true
        } catch (error) {
          results.failed++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'

          // Handle duplicate username/unique code errors
          if (
            errorMessage.toLowerCase().includes('duplicate') ||
            errorMessage.toLowerCase().includes('already exists')
          ) {
            results.errors.push({
              row: rowIndex,
              message: 'Username sudah ada: ' + errorMessage,
              data: record,
            })
          } else {
            results.errors.push({
              row: rowIndex,
              message: 'Error: ' + errorMessage,
              data: record,
            })
          }
          return null
        }
      })

      // Wait for the current batch to complete
      await Promise.all(batchPromises)

      // Clear cache after each batch
      await cache.del('users:all')
      await cache.del('classes:all')
    }

    return NextResponse.json({
      message: `Bulk upload selesai. Berhasil: ${results.success}, Gagal: ${results.failed} dari ${results.total}`,
      results,
    })
  } catch (error) {
    console.error('Bulk upload error:', error instanceof Error ? error.stack : error)
    return NextResponse.json(
      {
        message: 'Gagal memproses bulk upload',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
