/**
 * Student entity representing a student user in the system
 */
export interface Student {
  id: number
  uniqueCode: string // UUID for QR code
  googleEmail: string | null
  nis?: string // Optional
  name: string
  whatsapp?: string
  classId?: number
  className?: string // Derived from class relation
  username: string // Username for student login
  passwordHash: string // Hashed password for student login
  gender?: 'male' | 'female' // Gender of the student
  role: 'student' // Added role discriminator
  createdAt: Date
  updatedAt?: Date
}

/**
 * Data required to create a new student (e.g., by an Admin)
 */
export interface CreateStudentDTO {
  // googleEmail and uniqueCode will be handled by the use case/system
  name: string
  username: string // Username for student login
  password: string // Password in clear text (will be hashed)
  googleEmail?: string | null
  classId?: number // Make this optional or required based on your business logic
  nis?: string
  whatsapp?: string
  gender?: 'male' | 'female' // Gender of the student
}

/**
 * Data for updating a student's profile
 */
export interface UpdateStudentDTO {
  name?: string
  nis?: string | null
  whatsapp?: string | null
  classId?: number | null
  googleEmail?: string | null
  gender?: 'male' | 'female' | null
}
