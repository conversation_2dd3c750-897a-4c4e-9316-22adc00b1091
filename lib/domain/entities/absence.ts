/**
 * Attendance type enum
 */
export enum AttendanceType {
  // Prayer attendance (existing)
  ZUHR = 'Zuhr',
  ASR = 'Asr',
  IJIN = 'Ijin',

  // Departure (existing)
  DISMISSAL = 'Pulang',

  // School attendance (new)
  ENTRY = 'Entry',
  LATE_ENTRY = 'Late Entry',
  EXCUSED_ABSENCE = 'Excused Absence',
  TEMPORARY_LEAVE = 'Temporary Leave',
  RETURN_FROM_LEAVE = 'Return from Leave',
  SICK = 'Sick',
}

/**
 * Absence entity representing a student's attendance record
 */
export interface Absence {
  id: number
  uniqueCode: string // References Student.uniqueCode
  type: AttendanceType
  recordedAt: Date // Timestamp when the attendance was recorded
  createdAt: Date
}

/**
 * Data required to create a new absence record
 */
export interface CreateAbsenceDTO {
  uniqueCode: string
  type: AttendanceType
  recordedAt: Date
}

/**
 * Attendance summary for reporting
 */
export interface AttendanceSummary {
  summaryDate: Date | string
  uniqueCode: string
  name: string
  className: string

  // Prayer attendance fields
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null

  // School attendance fields
  entry?: boolean
  entryTime?: string | null
  lateEntry?: boolean
  lateEntryTime?: string | null
  excusedAbsence?: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave?: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave?: boolean
  returnFromLeaveTime?: string | null
  sick?: boolean
  sickTime?: string | null

  // For weekly reports
  weeklyRecords?: any[]

  // For aggregated reports (30 days, monthly, yearly)
  aggregatedCounts?: {
    zuhr: number
    asr: number
    dismissal: number
    ijin: number
    entry: number
    lateEntry: number
    excusedAbsence: number
    temporaryLeave: number
    returnFromLeave: number
    sick: number
  }

  updatedAt?: Date
}
