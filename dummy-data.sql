-- Dummy Data Script for ShalatYuk Database
-- This script creates 100 students with comprehensive attendance data
-- Including all attendance types: prayer, school, sick, ijin, etc.

-- First, let's create some classes
INSERT INTO classes (name) VALUES 
('X IPA 1'),
('X IPA 2'), 
('X IPA 3'),
('X IPS 1'),
('X IPS 2'),
('XI IPA 1'),
('XI IPA 2'),
('XI IPA 3'),
('XI IPS 1'),
('XI IPS 2'),
('XII IPA 1'),
('XII IPA 2'),
('XII IPA 3'),
('XII IPS 1'),
('XII IPS 2')
ON CONFLICT (name) DO NOTHING;

-- Create 100 students with realistic data
-- We'll use a function to generate unique codes and distribute across classes
DO $$
DECLARE
    i INTEGER;
    class_ids INTEGER[];
    selected_class_id INTEGER;
    unique_code_val VARCHAR(36);
    username_val VARCHAR(50);
    password_hash_val VARCHAR(255);
    student_names TEXT[] := ARRAY[
        '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> <PERSON>',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON><PERSON>',
        '<PERSON><PERSON> <PERSON><PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> <PERSON>',
        '<PERSON><PERSON> <PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON><PERSON> <PERSON>tuti', 'Andi Wijaya',
        'Mega Sari', 'Toni Hermawan', 'Lina Marliana', 'Reza Pratama', 'Dina Kartika',
        'Farid Rahman', 'Sinta Dewi', 'Bayu Aji', 'Lia Permatasari', 'Irwan Setiawan',
        'Novi Rahayu', 'Dimas Pratama', 'Eka Putri', 'Rizal Ramadhan', 'Wulan Dari',
        'Adi Nugroho', 'Tari Melati', 'Gilang Ramadhan', 'Siska Amelia', 'Yoga Pratama',
        'Desi Ratnasari', 'Fikri Hakim', 'Nuri Handayani', 'Arif Budiman', 'Yanti Sari',
        'Dedy Setiawan', 'Rina Susanti', 'Anto Wijaya', 'Lilis Suryani', 'Beni Kurniawan',
        'Sari Wulandari', 'Hadi Pranoto', 'Nia Rahmawati', 'Edi Susanto', 'Tina Marlina',
        'Roni Setiadi', 'Diah Ayu', 'Ari Wibowo', 'Sinta Maharani', 'Doni Pratama',
        'Evi Susanti', 'Gani Rahman', 'Lusi Handayani', 'Joni Setiawan', 'Rini Astuti',
        'Ade Kurniawan', 'Sari Indah', 'Budi Raharjo', 'Nita Sari', 'Dedi Rahman',
        'Yuni Marlina', 'Eko Setiadi', 'Lia Wulandari', 'Agus Pratama', 'Dina Sari',
        'Rudi Setiawan', 'Fitri Rahayu', 'Joko Susanto', 'Mega Putri', 'Hendra Wijaya',
        'Sinta Rahmawati', 'Bayu Setiadi', 'Rina Handayani', 'Dimas Kurniawan', 'Novi Sari',
        'Farid Setiawan', 'Lina Rahayu', 'Andi Pratama', 'Yanti Wulandari', 'Toni Setiadi',
        'Desi Handayani', 'Rizal Wijaya', 'Wulan Sari', 'Adi Setiawan', 'Tari Rahayu'
    ];
BEGIN
    -- Get all class IDs
    SELECT ARRAY(SELECT id FROM classes ORDER BY id) INTO class_ids;
    
    -- Create 100 students
    FOR i IN 1..100 LOOP
        -- Generate unique code (UUID-like)
        unique_code_val := gen_random_uuid()::text;
        
        -- Generate username
        username_val := 'student' || LPAD(i::text, 3, '0');
        
        -- Generate password hash (bcrypt hash of 'password123')
        password_hash_val := '$2b$10$rOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkz';
        
        -- Select random class
        selected_class_id := class_ids[1 + (i - 1) % array_length(class_ids, 1)];
        
        -- Insert student
        INSERT INTO users (
            role, unique_code, name, username, password_hash, class_id, 
            nis, whatsapp, created_at
        ) VALUES (
            'student',
            unique_code_val,
            student_names[1 + (i - 1) % array_length(student_names, 1)],
            username_val,
            password_hash_val,
            selected_class_id,
            '2024' || LPAD(i::text, 4, '0'), -- NIS format: 20240001, 20240002, etc.
            '08' || LPAD((1000000000 + i)::text, 10, '0'), -- WhatsApp format
            NOW() - INTERVAL '30 days' + (i || ' hours')::INTERVAL
        );
    END LOOP;
END $$;

-- Now generate comprehensive attendance data for the last 30 days
-- This will create realistic patterns including all attendance types
DO $$
DECLARE
    student_record RECORD;
    day_offset INTEGER;
    current_date_val DATE;
    attendance_time TIMESTAMP;
    random_factor FLOAT;
    attendance_pattern INTEGER; -- 1=excellent, 2=good, 3=average, 4=poor, 5=problematic
BEGIN
    -- For each student, generate 30 days of attendance data
    FOR student_record IN
        SELECT unique_code, name, id,
               -- Assign attendance pattern based on student ID
               CASE
                   WHEN id % 5 = 1 THEN 1 -- 20% excellent students
                   WHEN id % 5 = 2 THEN 2 -- 20% good students
                   WHEN id % 5 = 3 THEN 3 -- 20% average students
                   WHEN id % 5 = 4 THEN 4 -- 20% poor students
                   ELSE 5 -- 20% problematic students
               END as pattern
        FROM users WHERE role = 'student'
    LOOP
        attendance_pattern := student_record.pattern;

        -- Generate attendance for last 30 days
        FOR day_offset IN 0..29 LOOP
            current_date_val := CURRENT_DATE - INTERVAL '29 days' + (day_offset || ' days')::INTERVAL;

            -- Skip weekends (Saturday = 6, Sunday = 0)
            IF EXTRACT(DOW FROM current_date_val) NOT IN (0, 6) THEN
                random_factor := random();

                -- Generate different attendance patterns based on student type
                CASE attendance_pattern
                    WHEN 1 THEN -- Excellent students (95% attendance)
                        IF random_factor < 0.95 THEN
                            -- Morning entry
                            attendance_time := current_date_val + TIME '07:00:00' + (random() * INTERVAL '30 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_record.unique_code, 'Entry', attendance_time);

                            -- Zuhr prayer (98% attendance)
                            IF random() < 0.98 THEN
                                attendance_time := current_date_val + TIME '12:00:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Zuhr', attendance_time);
                            END IF;

                            -- Asr prayer (95% attendance)
                            IF random() < 0.95 THEN
                                attendance_time := current_date_val + TIME '15:00:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Asr', attendance_time);
                            END IF;

                            -- Dismissal (98% attendance)
                            IF random() < 0.98 THEN
                                attendance_time := current_date_val + TIME '16:00:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Pulang', attendance_time);
                            END IF;
                        ELSE
                            -- Occasionally sick or ijin
                            IF random() < 0.7 THEN
                                attendance_time := current_date_val + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Sick', attendance_time);
                            ELSE
                                attendance_time := current_date_val + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Ijin', attendance_time);
                            END IF;
                        END IF;

                    WHEN 2 THEN -- Good students (85% attendance)
                        IF random_factor < 0.85 THEN
                            -- Morning entry (sometimes late)
                            IF random() < 0.9 THEN
                                attendance_time := current_date + TIME '07:00:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Entry', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '07:30:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Late Entry', attendance_time);
                            END IF;

                            -- Zuhr prayer (90% attendance)
                            IF random() < 0.90 THEN
                                attendance_time := current_date + TIME '12:00:00' + (random() * INTERVAL '45 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Zuhr', attendance_time);
                            END IF;

                            -- Asr prayer (85% attendance)
                            IF random() < 0.85 THEN
                                attendance_time := current_date + TIME '15:00:00' + (random() * INTERVAL '45 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Asr', attendance_time);
                            END IF;

                            -- Dismissal (95% attendance)
                            IF random() < 0.95 THEN
                                attendance_time := current_date + TIME '16:00:00' + (random() * INTERVAL '30 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Pulang', attendance_time);
                            END IF;
                        ELSE
                            -- Sick, ijin, or excused absence
                            random_factor := random();
                            IF random_factor < 0.5 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Sick', attendance_time);
                            ELSIF random_factor < 0.8 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Ijin', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Excused Absence', attendance_time);
                            END IF;
                        END IF;

                    WHEN 3 THEN -- Average students (70% attendance)
                        IF random_factor < 0.70 THEN
                            -- Morning entry (often late)
                            IF random() < 0.7 THEN
                                attendance_time := current_date + TIME '07:00:00' + (random() * INTERVAL '45 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Entry', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '07:30:00' + (random() * INTERVAL '60 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Late Entry', attendance_time);
                            END IF;

                            -- Zuhr prayer (75% attendance)
                            IF random() < 0.75 THEN
                                attendance_time := current_date + TIME '12:00:00' + (random() * INTERVAL '60 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Zuhr', attendance_time);
                            END IF;

                            -- Asr prayer (70% attendance)
                            IF random() < 0.70 THEN
                                attendance_time := current_date + TIME '15:00:00' + (random() * INTERVAL '60 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Asr', attendance_time);
                            END IF;

                            -- Dismissal (85% attendance)
                            IF random() < 0.85 THEN
                                attendance_time := current_date + TIME '16:00:00' + (random() * INTERVAL '45 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Pulang', attendance_time);
                            END IF;
                        ELSE
                            -- Various absence types
                            random_factor := random();
                            IF random_factor < 0.4 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Sick', attendance_time);
                            ELSIF random_factor < 0.7 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Ijin', attendance_time);
                            ELSIF random_factor < 0.9 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Excused Absence', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Temporary Leave', attendance_time);
                            END IF;
                        END IF;

                    WHEN 4 THEN -- Poor students (50% attendance)
                        IF random_factor < 0.50 THEN
                            -- Morning entry (frequently late)
                            IF random() < 0.5 THEN
                                attendance_time := current_date + TIME '07:00:00' + (random() * INTERVAL '60 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Entry', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '07:45:00' + (random() * INTERVAL '90 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Late Entry', attendance_time);
                            END IF;

                            -- Zuhr prayer (60% attendance)
                            IF random() < 0.60 THEN
                                attendance_time := current_date + TIME '12:00:00' + (random() * INTERVAL '90 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Zuhr', attendance_time);
                            END IF;

                            -- Asr prayer (50% attendance)
                            IF random() < 0.50 THEN
                                attendance_time := current_date + TIME '15:00:00' + (random() * INTERVAL '90 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Asr', attendance_time);
                            END IF;

                            -- Dismissal (70% attendance)
                            IF random() < 0.70 THEN
                                attendance_time := current_date + TIME '16:00:00' + (random() * INTERVAL '60 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Pulang', attendance_time);
                            END IF;
                        ELSE
                            -- High absence rate with various types
                            random_factor := random();
                            IF random_factor < 0.3 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Sick', attendance_time);
                            ELSIF random_factor < 0.6 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Ijin', attendance_time);
                            ELSIF random_factor < 0.8 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Excused Absence', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Temporary Leave', attendance_time);
                            END IF;
                        END IF;

                    WHEN 5 THEN -- Problematic students (30% attendance)
                        IF random_factor < 0.30 THEN
                            -- Morning entry (very frequently late or absent)
                            IF random() < 0.3 THEN
                                attendance_time := current_date + TIME '07:00:00' + (random() * INTERVAL '90 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Entry', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '08:00:00' + (random() * INTERVAL '120 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Late Entry', attendance_time);
                            END IF;

                            -- Zuhr prayer (40% attendance)
                            IF random() < 0.40 THEN
                                attendance_time := current_date + TIME '12:00:00' + (random() * INTERVAL '120 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Zuhr', attendance_time);
                            END IF;

                            -- Asr prayer (30% attendance)
                            IF random() < 0.30 THEN
                                attendance_time := current_date + TIME '15:00:00' + (random() * INTERVAL '120 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Asr', attendance_time);
                            END IF;

                            -- Dismissal (50% attendance)
                            IF random() < 0.50 THEN
                                attendance_time := current_date + TIME '16:00:00' + (random() * INTERVAL '90 minutes');
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Pulang', attendance_time);
                            END IF;
                        ELSE
                            -- Very high absence rate with all types
                            random_factor := random();
                            IF random_factor < 0.25 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Sick', attendance_time);
                            ELSIF random_factor < 0.5 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Ijin', attendance_time);
                            ELSIF random_factor < 0.7 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Excused Absence', attendance_time);
                            ELSIF random_factor < 0.85 THEN
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Temporary Leave', attendance_time);
                            ELSE
                                attendance_time := current_date + TIME '08:00:00';
                                INSERT INTO absences (unique_code, type, recorded_at)
                                VALUES (student_record.unique_code, 'Return from Leave', attendance_time);
                            END IF;
                        END IF;
                END CASE;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- Add some admin users for testing
INSERT INTO users (role, name, username, password_hash, created_at) VALUES
('super_admin', 'Super Admin', 'superadmin', '$2b$10$rOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkz', NOW()),
('admin', 'Admin User', 'admin', '$2b$10$rOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkz', NOW()),
('teacher', 'Teacher User', 'teacher', '$2b$10$rOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkz', NOW()),
('receptionist', 'Receptionist User', 'receptionist', '$2b$10$rOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkzjkzjkzjOzJJjkzjkzjkz', NOW())
ON CONFLICT (username) DO NOTHING;

-- Display summary of created data
SELECT
    'Data Summary' as info,
    (SELECT COUNT(*) FROM classes) as total_classes,
    (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
    (SELECT COUNT(*) FROM users WHERE role != 'student') as total_staff,
    (SELECT COUNT(*) FROM absences) as total_attendance_records;

-- Display attendance type distribution
SELECT
    type as attendance_type,
    COUNT(*) as total_records,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM absences), 2) as percentage
FROM absences
GROUP BY type
ORDER BY total_records DESC;

-- Display student distribution by class
SELECT
    c.name as class_name,
    COUNT(u.id) as student_count
FROM classes c
LEFT JOIN users u ON c.id = u.class_id AND u.role = 'student'
GROUP BY c.id, c.name
ORDER BY c.name;

COMMIT;
