-- Migration to add gender column to users table
-- This migration adds gender support without losing existing data

BEGIN;

-- Add gender enum type
DO $$ BEGIN
    CREATE TYPE gender_type AS ENUM ('male', 'female');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add gender column to users table (nullable initially)
ALTER TABLE users ADD COLUMN IF NOT EXISTS gender gender_type;

-- Set default gender values for existing users based on common Indonesian names
-- This is a best-effort approach for existing data
UPDATE users SET gender = (CASE
    -- Common male names
    WHEN name ILIKE '%ahmad%' OR name ILIKE '%muhammad%' OR name ILIKE '%budi%' 
         OR name ILIKE '%andi%' OR name ILIKE '%doni%' OR name ILIKE '%eko%'
         OR name ILIKE '%agus%' OR name ILIKE '%joko%' OR name ILIKE '%hendra%'
         OR name ILIKE '%bambang%' OR name ILIKE '%wahyu%' OR name ILIKE '%rudi%'
         OR name ILIKE '%dedi%' OR name ILIKE '%farid%' OR name ILIKE '%bayu%'
         OR name ILIKE '%dimas%' OR name ILIKE '%rizal%' OR name ILIKE '%adi%'
         OR name ILIKE '%gilang%' OR name ILIKE '%arif%' OR name ILIKE '%yoga%'
         OR name ILIKE '%fikri%' OR name ILIKE '%anto%' OR name ILIKE '%beni%'
         OR name ILIKE '%hadi%' OR name ILIKE '%edi%' OR name ILIKE '%roni%'
         OR name ILIKE '%ari%' OR name ILIKE '%gani%' OR name ILIKE '%joni%'
         OR name ILIKE '%ade%' OR name ILIKE '%irwan%' OR name ILIKE '%toni%'
         OR name ILIKE '%reza%' OR name ILIKE '%admin%' OR name ILIKE '%super%'
         OR name ILIKE '%teacher%' OR name ILIKE '%receptionist%'
    THEN 'male'
    
    -- Common female names  
    WHEN name ILIKE '%siti%' OR name ILIKE '%dewi%' OR name ILIKE '%aisyah%'
         OR name ILIKE '%ratna%' OR name ILIKE '%maya%' OR name ILIKE '%lestari%'
         OR name ILIKE '%indah%' OR name ILIKE '%sari%' OR name ILIKE '%fitri%'
         OR name ILIKE '%nita%' OR name ILIKE '%rina%' OR name ILIKE '%yuni%'
         OR name ILIKE '%mega%' OR name ILIKE '%lina%' OR name ILIKE '%dina%'
         OR name ILIKE '%sinta%' OR name ILIKE '%lia%' OR name ILIKE '%novi%'
         OR name ILIKE '%eka%' OR name ILIKE '%wulan%' OR name ILIKE '%tari%'
         OR name ILIKE '%siska%' OR name ILIKE '%desi%' OR name ILIKE '%nuri%'
         OR name ILIKE '%yanti%' OR name ILIKE '%rini%' OR name ILIKE '%lilis%'
         OR name ILIKE '%nia%' OR name ILIKE '%tina%' OR name ILIKE '%diah%'
         OR name ILIKE '%evi%' OR name ILIKE '%lusi%' OR name ILIKE '%nurhaliza%'
         OR name ILIKE '%permata%' OR name ILIKE '%melati%' OR name ILIKE '%handayani%'
         OR name ILIKE '%anggraini%' OR name ILIKE '%marlina%' OR name ILIKE '%astuti%'
         OR name ILIKE '%kartika%' OR name ILIKE '%permatasari%' OR name ILIKE '%rahayu%'
         OR name ILIKE '%putri%' OR name ILIKE '%dari%' OR name ILIKE '%amelia%'
         OR name ILIKE '%ratnasari%' OR name ILIKE '%susanti%' OR name ILIKE '%suryani%'
         OR name ILIKE '%wulandari%' OR name ILIKE '%rahmawati%' OR name ILIKE '%marlina%'
         OR name ILIKE '%maharani%' OR name ILIKE '%rahmawati%'
    THEN 'female'
    
    -- Default to male for ambiguous names (can be updated manually later)
    ELSE 'male'
END)::gender_type
WHERE gender IS NULL;

-- Add comment to the column
COMMENT ON COLUMN users.gender IS 'Gender of the user (male/female)';

-- Display summary of gender distribution
SELECT 
    'Gender Distribution Summary' as info,
    gender,
    COUNT(*) as total_users,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users), 2) as percentage
FROM users 
WHERE gender IS NOT NULL
GROUP BY gender
ORDER BY total_users DESC;

COMMIT;
