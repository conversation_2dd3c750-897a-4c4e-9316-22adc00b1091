-- Attendance Dummy Data Script
-- This script generates realistic attendance data for all students

BEGIN;

-- Generate attendance data for the last 30 days
DO $$
DECLARE
    student_rec RECORD;
    day_counter INTEGER;
    attendance_date DATE;
    base_time TIMESTAMP;
    random_val FLOAT;
    attendance_pattern INTEGER;
BEGIN
    -- Loop through each student
    FOR student_rec IN 
        SELECT unique_code, id, name FROM users WHERE role = 'student' ORDER BY id
    LOOP
        -- Assign attendance pattern based on student ID (for variety)
        attendance_pattern := (student_rec.id % 5) + 1;
        
        -- Generate 30 days of attendance (skip weekends)
        FOR day_counter IN 0..29 LOOP
            attendance_date := CURRENT_DATE - INTERVAL '29 days' + (day_counter || ' days')::INTERVAL;
            
            -- Skip weekends (0=Sunday, 6=Saturday)
            IF EXTRACT(DOW FROM attendance_date) NOT IN (0, 6) THEN
                random_val := random();
                
                -- Pattern 1: Excellent students (90% attendance)
                IF attendance_pattern = 1 THEN
                    IF random_val < 0.90 THEN
                        -- Morning Entry
                        base_time := attendance_date + TIME '07:00:00' + (random() * INTERVAL '30 minutes');
                        INSERT INTO absences (unique_code, type, recorded_at) 
                        VALUES (student_rec.unique_code, 'Entry', base_time);
                        
                        -- Zuhr Prayer (95% when present)
                        IF random() < 0.95 THEN
                            base_time := attendance_date + TIME '12:00:00' + (random() * INTERVAL '30 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Zuhr', base_time);
                        END IF;
                        
                        -- Asr Prayer (90% when present)
                        IF random() < 0.90 THEN
                            base_time := attendance_date + TIME '15:00:00' + (random() * INTERVAL '30 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Asr', base_time);
                        END IF;
                        
                        -- Dismissal (95% when present)
                        IF random() < 0.95 THEN
                            base_time := attendance_date + TIME '16:00:00' + (random() * INTERVAL '30 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Pulang', base_time);
                        END IF;
                    ELSE
                        -- Absent - record reason
                        IF random() < 0.6 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Sick', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Ijin', base_time);
                        END IF;
                    END IF;
                    
                -- Pattern 2: Good students (80% attendance)
                ELSIF attendance_pattern = 2 THEN
                    IF random_val < 0.80 THEN
                        -- Morning Entry (sometimes late)
                        IF random() < 0.85 THEN
                            base_time := attendance_date + TIME '07:00:00' + (random() * INTERVAL '45 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Entry', base_time);
                        ELSE
                            base_time := attendance_date + TIME '07:30:00' + (random() * INTERVAL '45 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Late Entry', base_time);
                        END IF;
                        
                        -- Zuhr Prayer (85% when present)
                        IF random() < 0.85 THEN
                            base_time := attendance_date + TIME '12:00:00' + (random() * INTERVAL '45 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Zuhr', base_time);
                        END IF;
                        
                        -- Asr Prayer (80% when present)
                        IF random() < 0.80 THEN
                            base_time := attendance_date + TIME '15:00:00' + (random() * INTERVAL '45 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Asr', base_time);
                        END IF;
                        
                        -- Dismissal (90% when present)
                        IF random() < 0.90 THEN
                            base_time := attendance_date + TIME '16:00:00' + (random() * INTERVAL '45 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Pulang', base_time);
                        END IF;
                    ELSE
                        -- Absent - various reasons
                        random_val := random();
                        IF random_val < 0.4 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Sick', base_time);
                        ELSIF random_val < 0.7 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Ijin', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Excused Absence', base_time);
                        END IF;
                    END IF;
                    
                -- Pattern 3: Average students (70% attendance)
                ELSIF attendance_pattern = 3 THEN
                    IF random_val < 0.70 THEN
                        -- Morning Entry (often late)
                        IF random() < 0.70 THEN
                            base_time := attendance_date + TIME '07:00:00' + (random() * INTERVAL '60 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Entry', base_time);
                        ELSE
                            base_time := attendance_date + TIME '07:45:00' + (random() * INTERVAL '60 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Late Entry', base_time);
                        END IF;
                        
                        -- Zuhr Prayer (75% when present)
                        IF random() < 0.75 THEN
                            base_time := attendance_date + TIME '12:00:00' + (random() * INTERVAL '60 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Zuhr', base_time);
                        END IF;
                        
                        -- Asr Prayer (70% when present)
                        IF random() < 0.70 THEN
                            base_time := attendance_date + TIME '15:00:00' + (random() * INTERVAL '60 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Asr', base_time);
                        END IF;
                        
                        -- Dismissal (80% when present)
                        IF random() < 0.80 THEN
                            base_time := attendance_date + TIME '16:00:00' + (random() * INTERVAL '60 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Pulang', base_time);
                        END IF;
                    ELSE
                        -- Absent - various reasons including temporary leave
                        random_val := random();
                        IF random_val < 0.3 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Sick', base_time);
                        ELSIF random_val < 0.6 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Ijin', base_time);
                        ELSIF random_val < 0.8 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Excused Absence', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at) 
                            VALUES (student_rec.unique_code, 'Temporary Leave', base_time);
                        END IF;
                    END IF;

                -- Pattern 4: Poor students (50% attendance)
                ELSIF attendance_pattern = 4 THEN
                    IF random_val < 0.50 THEN
                        -- Morning Entry (frequently late)
                        IF random() < 0.50 THEN
                            base_time := attendance_date + TIME '07:00:00' + (random() * INTERVAL '90 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Entry', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00' + (random() * INTERVAL '90 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Late Entry', base_time);
                        END IF;

                        -- Zuhr Prayer (60% when present)
                        IF random() < 0.60 THEN
                            base_time := attendance_date + TIME '12:00:00' + (random() * INTERVAL '90 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Zuhr', base_time);
                        END IF;

                        -- Asr Prayer (50% when present)
                        IF random() < 0.50 THEN
                            base_time := attendance_date + TIME '15:00:00' + (random() * INTERVAL '90 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Asr', base_time);
                        END IF;

                        -- Dismissal (70% when present)
                        IF random() < 0.70 THEN
                            base_time := attendance_date + TIME '16:00:00' + (random() * INTERVAL '90 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Pulang', base_time);
                        END IF;
                    ELSE
                        -- High absence rate
                        random_val := random();
                        IF random_val < 0.25 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Sick', base_time);
                        ELSIF random_val < 0.5 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Ijin', base_time);
                        ELSIF random_val < 0.7 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Excused Absence', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Temporary Leave', base_time);
                        END IF;
                    END IF;

                -- Pattern 5: Problematic students (30% attendance)
                ELSE
                    IF random_val < 0.30 THEN
                        -- Morning Entry (very frequently late)
                        IF random() < 0.30 THEN
                            base_time := attendance_date + TIME '07:00:00' + (random() * INTERVAL '120 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Entry', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:30:00' + (random() * INTERVAL '120 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Late Entry', base_time);
                        END IF;

                        -- Zuhr Prayer (40% when present)
                        IF random() < 0.40 THEN
                            base_time := attendance_date + TIME '12:00:00' + (random() * INTERVAL '120 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Zuhr', base_time);
                        END IF;

                        -- Asr Prayer (30% when present)
                        IF random() < 0.30 THEN
                            base_time := attendance_date + TIME '15:00:00' + (random() * INTERVAL '120 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Asr', base_time);
                        END IF;

                        -- Dismissal (50% when present)
                        IF random() < 0.50 THEN
                            base_time := attendance_date + TIME '16:00:00' + (random() * INTERVAL '120 minutes');
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Pulang', base_time);
                        END IF;
                    ELSE
                        -- Very high absence rate with all types
                        random_val := random();
                        IF random_val < 0.2 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Sick', base_time);
                        ELSIF random_val < 0.4 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Ijin', base_time);
                        ELSIF random_val < 0.6 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Excused Absence', base_time);
                        ELSIF random_val < 0.8 THEN
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Temporary Leave', base_time);
                        ELSE
                            base_time := attendance_date + TIME '08:00:00';
                            INSERT INTO absences (unique_code, type, recorded_at)
                            VALUES (student_rec.unique_code, 'Return from Leave', base_time);
                        END IF;
                    END IF;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- Display summary of created data
SELECT
    'Attendance Data Summary' as info,
    (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
    (SELECT COUNT(*) FROM absences) as total_attendance_records,
    (SELECT COUNT(DISTINCT DATE(recorded_at)) FROM absences) as days_with_data;

-- Display attendance type distribution
SELECT
    type as attendance_type,
    COUNT(*) as total_records,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM absences), 2) as percentage
FROM absences
GROUP BY type
ORDER BY total_records DESC;

COMMIT;
