const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
require('dotenv').config({ path: '.env.local' });

async function testConnection() {
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    console.error('❌ DATABASE_URL not found in environment');
    process.exit(1);
  }

  console.log('🔄 Testing database connection...');
  console.log('Connection string:', connectionString.replace(/:[^:@]*@/, ':****@'));

  try {
    const sql = postgres(connectionString);
    const db = drizzle(sql);
    
    // Test basic connection
    const result = await sql`SELECT version(), current_database(), current_user`;
    console.log('✅ Database connection successful!');
    console.log('Database version:', result[0].version);
    console.log('Current database:', result[0].current_database);
    console.log('Current user:', result[0].current_user);
    
    // Check if tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('\n📋 Existing tables:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
    // Check enum types
    const enums = await sql`
      SELECT t.typname as enum_name, e.enumlabel as enum_value
      FROM pg_type t 
      JOIN pg_enum e ON t.oid = e.enumtypid  
      ORDER BY t.typname, e.enumsortorder
    `;
    
    console.log('\n🏷️  Existing enum types:');
    const enumGroups = {};
    enums.forEach(row => {
      if (!enumGroups[row.enum_name]) {
        enumGroups[row.enum_name] = [];
      }
      enumGroups[row.enum_name].push(row.enum_value);
    });
    
    Object.keys(enumGroups).forEach(enumName => {
      console.log(`  - ${enumName}: [${enumGroups[enumName].join(', ')}]`);
    });
    
    await sql.end();
    console.log('\n✅ Connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
}

testConnection();
