import React from 'react'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Search } from 'lucide-react'

export interface StudentClass {
  id: number
  name: string
}

interface StudentFilterBarProps {
  searchQuery: string
  onSearchChange: (value: string) => void
  classFilter: string
  onClassFilterChange: (value: string) => void
  genderFilter: string
  onGenderFilterChange: (value: string) => void
  classes: StudentClass[]
}

export const StudentFilterBar: React.FC<StudentFilterBarProps> = ({
  searchQuery,
  onSearchChange,
  classFilter,
  onClassFilterChange,
  genderFilter,
  onGenderFilterChange,
  classes,
}) => {
  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
        <Input
          placeholder="Cari nama, username, kode, NIS, gender, atau kelas..."
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>
      <Select value={classFilter} onValueChange={onClassFilterChange}>
        <SelectTrigger className="w-full md:w-[180px]">
          <SelectValue placeholder="Filter kelas" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Kelas</SelectItem>
          {classes.map(cls => (
            <SelectItem key={cls.id} value={String(cls.id)}>
              {cls.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Select value={genderFilter} onValueChange={onGenderFilterChange}>
        <SelectTrigger className="w-full md:w-[150px]">
          <SelectValue placeholder="Filter gender" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Gender</SelectItem>
          <SelectItem value="male">Laki-laki</SelectItem>
          <SelectItem value="female">Perempuan</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
