import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Edit, Trash2 } from 'lucide-react'

export interface StudentTableRow {
  id: number
  uniqueCode?: string
  username?: string
  name: string
  gender?: 'male' | 'female'
  nis?: string
  className?: string
}

interface StudentTableProps {
  students: StudentTableRow[]
  selectedUsers: Set<number>
  onSelectUser: (userId: number) => void
  onEditUser: (user: StudentTableRow) => void
  onDeleteUser: (user: StudentTableRow) => void
  currentPage: number
  itemsPerPage: number
}

export const StudentTable: React.FC<StudentTableProps> = ({
  students,
  selectedUsers,
  onSelectUser,
  onEditUser,
  onDeleteUser,
  currentPage,
  itemsPerPage,
}) => {
  // Helper to display gender in Indonesian
  const getGenderLabel = (gender?: 'male' | 'female') => {
    if (gender === 'male') return 'Laki-laki'
    if (gender === 'female') return 'Perempuan'
    return '-'
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px]">
            {/* TODO: Add select all checkbox for bulk actions */}
          </TableHead>
          <TableHead className="w-[50px]">No</TableHead>
          <TableHead>ID/Kode Unik</TableHead>
          <TableHead>Username</TableHead>
          <TableHead>Nama</TableHead>
          <TableHead>Gender</TableHead>
          <TableHead>NIS</TableHead>
          <TableHead>Kelas</TableHead>
          <TableHead className="text-right">Aksi</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {students.length === 0 ? (
          <TableRow>
            <TableCell colSpan={9} className="h-24 text-center">
              Tidak ada data siswa
            </TableCell>
          </TableRow>
        ) : (
          students.map((student, index) => (
            <TableRow
              key={student.id}
              className={selectedUsers.has(student.id) ? 'bg-indigo-50 dark:bg-slate-800/60' : ''}
            >
              <TableCell>
                <input
                  type="checkbox"
                  className="h-5 w-5 cursor-pointer rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  checked={selectedUsers.has(student.id)}
                  onChange={() => onSelectUser(student.id)}
                  title="Pilih/batalkan pilih siswa ini"
                />
              </TableCell>
              <TableCell className="text-center font-medium">
                {(currentPage - 1) * itemsPerPage + index + 1}
              </TableCell>
              <TableCell>
                {student.uniqueCode
                  ? `${student.uniqueCode.substring(0, 8)}...`
                  : student.username || '-'}
              </TableCell>
              <TableCell>{student.username || '-'}</TableCell>
              <TableCell>{student.name}</TableCell>
              <TableCell>{getGenderLabel(student.gender)}</TableCell>
              <TableCell>{student.nis || '-'}</TableCell>
              <TableCell>{student.className || '-'}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" size="sm" onClick={() => onEditUser(student)}>
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                    onClick={() => onDeleteUser(student)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
