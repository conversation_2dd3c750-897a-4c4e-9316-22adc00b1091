import React from 'react'
import { <PERSON>, <PERSON>Header, CardTitle, CardContent } from '@/components/ui/card'

interface StudentStatsCardsProps {
  total: number
  withClass: number
  withoutClass: number
  filteredCount?: number
}

export const StudentStatsCards: React.FC<StudentStatsCardsProps> = ({
  total,
  withClass,
  withoutClass,
  filteredCount,
}) => {
  return (
    <div className="mb-4 mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
      <Card className="bg-white shadow-sm dark:bg-slate-800">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
            Total Siswa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{total}</div>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            {filteredCount !== undefined &&
              filteredCount !== total &&
              `${filteredCount} ditampilkan dari filter`}
          </p>
        </CardContent>
      </Card>
      <Card className="bg-white shadow-sm dark:bg-slate-800">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
            Siswa Berkelas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">{withClass}</div>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            Siswa yang sudah memiliki kelas
          </p>
        </CardContent>
      </Card>
      <Card className="bg-white shadow-sm dark:bg-slate-800">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
            Tanpa Kelas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
            {withoutClass}
          </div>
          <p className="text-xs text-slate-500 dark:text-slate-400">
            Siswa yang belum memiliki kelas
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
