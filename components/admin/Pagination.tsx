import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface PaginationProps {
  currentPage: number
  totalItems: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onItemsPerPageChange: (itemsPerPage: number) => void
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1)

  return (
    <div className="flex flex-col items-center justify-between space-y-4 border-t border-slate-200 px-4 py-4 dark:border-slate-700 sm:flex-row sm:space-y-0 sm:px-6">
      <div className="flex items-center space-x-4">
        <p className="text-sm text-slate-700 dark:text-slate-400">
          Menampilkan <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>{' '}
          sampai{' '}
          <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalItems)}</span>{' '}
          dari <span className="font-medium">{totalItems}</span> pengguna
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-slate-700 dark:text-slate-400">Tampilkan</span>
          <Select
            value={String(itemsPerPage)}
            onValueChange={value => onItemsPerPageChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder="10" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-slate-700 dark:text-slate-400">per halaman</span>
        </div>
      </div>
      <div className="flex flex-1 justify-between sm:justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
          disabled={currentPage === 1}
          className="text-xs"
        >
          Sebelumnya
        </Button>
        <div className="mx-2 hidden items-center space-x-1 sm:flex">
          {pageNumbers.map(i => (
            <Button
              key={i}
              variant={currentPage === i ? 'default' : 'outline'}
              size="sm"
              className={`h-8 w-8 p-0 text-xs ${
                totalPages > 7 && i !== 1 && i !== totalPages && Math.abs(currentPage - i) > 1
                  ? 'hidden'
                  : ''
              }`}
              onClick={() => onPageChange(i)}
            >
              {i}
            </Button>
          ))}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
          disabled={currentPage >= totalPages}
          className="text-xs"
        >
          Selanjutnya
        </Button>
      </div>
    </div>
  )
}
