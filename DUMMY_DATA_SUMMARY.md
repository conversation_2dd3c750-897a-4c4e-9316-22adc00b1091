# Dummy Data Summary for ShalatYuk Database

## Overview
Successfully created comprehensive dummy data for testing the ShalatYuk attendance system with realistic patterns and scenarios.

## Data Created

### 1. Classes (15 classes)
- **X IPA 1, X IPA 2, X IPA 3** (Grade 10 Science)
- **X IPS 1, X IPS 2** (Grade 10 Social Studies)
- **XI IPA 1, XI IPA 2, XI IPA 3** (Grade 11 Science)
- **XI IPS 1, XI IPS 2** (Grade 11 Social Studies)
- **XII IPA 1, XII IPA 2, XII IPA 3** (Grade 12 Science)
- **XII IPS 1, XII IPS 2** (Grade 12 Social Studies)

### 2. Students (101 students)
- **Distribution**: Evenly distributed across all classes (6-8 students per class)
- **Usernames**: student001 to student101
- **Password**: All students have password "password123" (hashed)
- **NIS**: Format 20240001 to 20240101
- **WhatsApp**: Format 081000000001 to 081000000101
- **Names**: Realistic Indonesian names

### 3. Staff Users (4 users)
- **Super Admin**: username `superadmin`, password `password123`
- **Admin**: username `admin`, password `password123`
- **Teacher**: username `teacher`, password `password123`
- **Receptionist**: username `receptionist`, password `password123`

### 4. Attendance Data (5,596 records)
Generated for the last 30 days (excluding weekends) with realistic patterns:

#### Attendance Types Distribution:
- **Pulang (Dismissal)**: 1,206 records (21.55%)
- **Zuhr (Midday Prayer)**: 1,118 records (19.98%)
- **Entry (Morning Entry)**: 1,085 records (19.39%)
- **Asr (Afternoon Prayer)**: 1,043 records (18.64%)
- **Late Entry**: 353 records (6.31%)
- **Sick**: 219 records (3.91%)
- **Ijin (Permission)**: 203 records (3.63%)
- **Excused Absence**: 163 records (2.91%)
- **Temporary Leave**: 148 records (2.64%)
- **Return from Leave**: 58 records (1.04%)

#### Student Attendance Patterns:
Students are categorized into 5 different attendance patterns based on their ID:

1. **Excellent Students (20%)**: 95% attendance rate
   - Rarely late, consistent prayer attendance
   - Occasional sick/ijin when absent

2. **Good Students (20%)**: 85% attendance rate
   - Sometimes late, good prayer attendance
   - Mix of sick/ijin/excused absence

3. **Average Students (20%)**: 70% attendance rate
   - Often late, moderate prayer attendance
   - Various absence types including temporary leave

4. **Poor Students (20%)**: 50% attendance rate
   - Frequently late, inconsistent attendance
   - High absence rate with various reasons

5. **Problematic Students (20%)**: 30% attendance rate
   - Very frequently late or absent
   - All types of absences including return from leave

## Data Coverage
- **Time Period**: Last 30 days (22 working days, excluding weekends)
- **Daily Records**: 239-275 attendance records per day
- **Student Participation**: All 101 students have attendance records
- **Realistic Timing**: 
  - Entry: 7:00-8:30 AM
  - Zuhr: 12:00-1:30 PM
  - Asr: 3:00-4:30 PM
  - Dismissal: 4:00-5:30 PM

## Testing Scenarios Covered

### Prayer Reports Testing:
- Students with perfect prayer attendance
- Students who skip prayers occasionally
- Students with ijin (permission not to pray)
- Mixed attendance patterns

### School Attendance Testing:
- On-time vs late entries
- Perfect attendance students
- Students with various absence types
- Temporary leave and return scenarios

### Analytics Testing:
- Weekly attendance summaries
- Monthly trend analysis
- Class-wise comparisons
- Attendance type distributions

## Database Credentials Used
- **Host**: localhost:5433
- **Database**: website
- **Username**: postgres
- **Password**: 976f56fddbdd7d7e7a86

## Files Created
1. `simple-dummy-data.sql` - Creates classes, students, and staff users
2. `attendance-dummy-data.sql` - Generates comprehensive attendance data
3. `dummy-data.sql` - Original complex script (not used due to syntax issues)

## Next Steps
You can now:
1. Test the prayer reports with realistic data
2. Test school attendance reports with various scenarios
3. Verify analytics and dashboard functionality
4. Test CSV export features with substantial data
5. Performance test with 100+ students and 5,000+ records

The data provides a comprehensive testing environment that mirrors real-world usage patterns for a school with 100+ students.
