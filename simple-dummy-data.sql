-- Simple Dummy Data Script for ShalatYuk Database
-- This script creates 100 students with comprehensive attendance data

BEGIN;

-- First, let's create some classes
INSERT INTO classes (name) VALUES 
('X IPA 1'), ('X IPA 2'), ('X IPA 3'),
('X IPS 1'), ('X IPS 2'),
('XI IPA 1'), ('XI IPA 2'), ('XI IPA 3'),
('XI IPS 1'), ('XI IPS 2'),
('XII IPA 1'), ('XII IPA 2'), ('XII IPA 3'),
('XII IPS 1'), ('XII IPS 2')
ON CONFLICT (name) DO NOTHING;

-- Create 100 students with realistic data
DO $$
DECLARE
    i INTEGER;
    class_ids INTEGER[];
    selected_class_id INTEGER;
    unique_code_val VARCHAR(36);
    username_val VARCHAR(50);
    password_hash_val VARCHAR(255);
    student_names TEXT[] := ARRAY[
        '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON>', '<PERSON>',
        '<PERSON><PERSON> <PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>liana', 'Reza Pratama', 'Dina Kartika',
        'Farid Rahman', 'Sinta Dewi', 'Bayu Aji', 'Lia Permatasari', 'Irwan Setiawan',
        'Novi Rahayu', 'Dimas Pratama', 'Eka Putri', 'Rizal Ramadhan', 'Wulan Dari',
        'Adi Nugroho', 'Tari Melati', 'Gilang Ramadhan', 'Siska Amelia', 'Yoga Pratama',
        'Desi Ratnasari', 'Fikri Hakim', 'Nuri Handayani', 'Arif Budiman', 'Yanti Sari',
        'Dedy Setiawan', 'Rina Susanti', 'Anto Wijaya', 'Lilis Suryani', 'Beni Kurniawan',
        'Sari Wulandari', 'Hadi Pranoto', 'Nia Rahmawati', 'Edi Susanto', 'Tina Marlina',
        'Roni Setiadi', 'Diah Ayu', 'Ari Wibowo', 'Sinta Maharani', 'Doni Pratama',
        'Evi Susanti', 'Gani Rahman', 'Lusi Handayani', 'Joni Setiawan', 'Rini Astuti',
        'Ade Kurniawan', 'Sari Indah', 'Budi Raharjo', 'Nita Sari', 'Dedi Rahman',
        'Yuni Marlina', 'Eko Setiadi', 'Lia Wulandari', 'Agus Pratama', 'Dina Sari',
        'Rudi Setiawan', 'Fitri Rahayu', 'Joko Susanto', 'Mega Putri', 'Hendra Wijaya',
        'Sinta Rahmawati', 'Bayu Setiadi', 'Rina Handayani', 'Dimas Kurniawan', 'Novi Sari',
        'Farid Setiawan', 'Lina Rahayu', 'Andi Pratama', 'Yanti Wulandari', 'Toni Setiadi',
        'Desi Handayani', 'Rizal Wijaya', 'Wulan Sari', 'Adi Setiawan', 'Tari Rahayu'
    ];
BEGIN
    -- Get all class IDs
    SELECT ARRAY(SELECT id FROM classes ORDER BY id) INTO class_ids;
    
    -- Create 100 students
    FOR i IN 1..100 LOOP
        -- Generate unique code (UUID-like)
        unique_code_val := gen_random_uuid()::text;
        
        -- Generate username
        username_val := 'student' || LPAD(i::text, 3, '0');
        
        -- Generate password hash (bcrypt hash of 'password123')
        password_hash_val := '$2b$10$K8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKz';
        
        -- Select class (distribute evenly)
        selected_class_id := class_ids[1 + (i - 1) % array_length(class_ids, 1)];
        
        -- Insert student
        INSERT INTO users (
            role, unique_code, name, username, password_hash, class_id, 
            nis, whatsapp, created_at
        ) VALUES (
            'student',
            unique_code_val,
            student_names[1 + (i - 1) % array_length(student_names, 1)],
            username_val,
            password_hash_val,
            selected_class_id,
            '2024' || LPAD(i::text, 4, '0'), -- NIS format: 20240001, 20240002, etc.
            '08' || LPAD((1000000000 + i)::text, 10, '0'), -- WhatsApp format
            NOW() - INTERVAL '30 days' + (i || ' hours')::INTERVAL
        );
    END LOOP;
END $$;

-- Add some admin users for testing (fix constraint issue)
INSERT INTO users (role, name, username, password_hash, created_at) VALUES 
('super_admin', 'Super Admin', 'superadmin', '$2b$10$K8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKz', NOW()),
('admin', 'Admin User', 'admin', '$2b$10$K8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKz', NOW()),
('teacher', 'Teacher User', 'teacher', '$2b$10$K8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKz', NOW()),
('receptionist', 'Receptionist User', 'receptionist', '$2b$10$K8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKzKzKzKzKzKzKOK8gTsqMhKz', NOW())
ON CONFLICT (username) DO NOTHING;

COMMIT;
